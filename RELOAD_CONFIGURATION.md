# FastAPI Reload Configuration

This document explains how the FastAPI server is configured to prevent ANY reloads when files change in the project.

## Problem

By default, FastAPI with `--reload` watches the entire project directory for file changes. This means that when the AI Project Generator creates files in the `generated_projects` folder, it triggers unnecessary server reloads, which can be disruptive during development.

## Solution

We completely disable the reload functionality (`reload=False`) to prevent any file changes from triggering server restarts.

## Configuration Methods

### 1. Start Script (`start-dev.sh`)

```bash
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 2. Alternative Start Script (`start-dev-precise.sh`)

A dedicated script with clear messaging about disabled reload behavior.

### 3. Main Application (`backend/app/main.py`)

```python
# Disable reload to prevent any file changes from restarting the server
uvicorn.run(
    "app.main:app",
    host=host,
    port=port,
    reload=False,  # Completely disable reload
    log_level="info",
)
```

### 4. Configuration File (`backend/uvicorn.toml`)

```toml
[tool.uvicorn]
host = "0.0.0.0"
port = 8000
reload = false
```

## Directory Structure

```
backend/
├── app/                    # ❌ NOT WATCHED - Reload disabled
│   ├── main.py
│   ├── models/
│   ├── services/
│   └── ...
├── generated_projects/     # ❌ NOT WATCHED - Reload disabled
│   ├── project1/
│   ├── project2/
│   └── ...
├── .venv/                  # ❌ NOT WATCHED - Reload disabled
├── __pycache__/           # ❌ NOT WATCHED - Reload disabled
└── ...
```

## Behavior

### ❌ Will NOT Trigger Reload (Reload Disabled):
- Changes to Python files in `backend/app/`
- New files added to `backend/app/`
- Modifications to `backend/app/main.py`
- Changes to models, services, etc. in `backend/app/`
- Files created in `generated_projects/`
- ZIP files created for downloads
- Cache files (`__pycache__/`)
- Virtual environment files (`.venv/`)
- Log files
- Temporary files
- ANY file changes anywhere in the project

### 🔄 Manual Restart Required:
- To apply any backend code changes, you must manually restart the server

## Testing

Run the test script to verify the configuration:

```bash
./test-reload-exclusion.sh
```

This script will:
1. Create files in `generated_projects/` (should NOT trigger reload)
2. Create a file in `app/` (should NOT trigger reload)
3. Clean up test files
4. Verify that NO reloads occur anywhere

## Usage

### Development (Recommended)
```bash
./start-dev.sh
```

### Development with Explicit Messaging
```bash
./start-dev-precise.sh
```

### Direct uvicorn
```bash
cd backend
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## Benefits

1. **No Reloads Ever**: Server never restarts due to file changes
2. **Stable Development**: Generated projects won't disrupt your work
3. **Better Performance**: No file system monitoring overhead
4. **Cleaner Logs**: No reload noise from any file changes
5. **Predictable Behavior**: Server only restarts when you manually restart it

## Troubleshooting

If you need to apply backend changes:

1. **Stop the server**: Press `Ctrl+C` in the terminal
2. **Restart the server**: Run `./start-dev.sh` again
3. **Alternative**: Use a process manager like `pm2` for easier restarts

## Development Workflow

With reload disabled, your development workflow becomes:

1. **Make backend changes** in `backend/app/`
2. **Manually restart** the server to apply changes
3. **Frontend changes** are still hot-reloaded automatically
4. **Generated projects** never interfere with your development

## Alternative Approaches

If you prefer automatic reloads for development, you can:

1. **Enable reload temporarily** by adding `--reload` flag when needed
2. **Use a separate development server** for testing changes
3. **Use IDE debugging** instead of server restarts
