import os
import uuid
import json
import re
from typing import Dict, Any, Optional, List
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from langchain.prompts import PromptTemplate
import openai
import requests

from app.models.project_models import (
    AIProvider,
    IntelligentProjectRequest,
    ProjectAnalysis,
    TechnologyStack,
    ModificationSuggestion,
    FileModification,
)


class IntelligentAIService:
    """Enhanced AI service with intelligent prompt understanding and modification capabilities"""

    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.huggingface_api_key = os.getenv("HUGGINGFACE_API_KEY")
        self.grok_api_key = os.getenv("GROK_API_KEY")

    async def analyze_project_requirements(
        self, request: IntelligentProjectRequest
    ) -> ProjectAnalysis:
        """Analyze project requirements from natural language prompt"""

        print(f"DEBUG: Creating analysis prompt for {request.ai_provider}")
        analysis_prompt = self._create_analysis_prompt(request)
        print(f"DEBUG: Analysis prompt created, length: {len(analysis_prompt)}")

        if request.ai_provider == AIProvider.OPENAI:
            print("DEBUG: Using OpenAI for analysis")
            response = await self._analyze_with_openai(analysis_prompt)
        elif request.ai_provider == AIProvider.HUGGINGFACE:
            print("DEBUG: Using Hugging Face for analysis")
            response = await self._analyze_with_huggingface(analysis_prompt)
        elif request.ai_provider == AIProvider.GROK:
            print("DEBUG: Using Grok for analysis")
            response = await self._analyze_with_grok(analysis_prompt)
        else:
            raise ValueError(f"Unsupported AI provider: {request.ai_provider}")

        print(f"DEBUG: AI response received, length: {len(response)}")
        analysis = self._parse_analysis_response(response, request)
        print(f"DEBUG: Analysis parsed successfully: {analysis.suggested_name}")
        return analysis

    async def generate_intelligent_project(
        self, analysis: ProjectAnalysis, request: IntelligentProjectRequest
    ) -> Dict[str, Any]:
        """Generate project based on AI analysis"""

        generation_prompt = self._create_generation_prompt(analysis, request)

        if request.ai_provider == AIProvider.OPENAI:
            response = await self._generate_with_openai(generation_prompt)
        elif request.ai_provider == AIProvider.HUGGINGFACE:
            response = await self._generate_with_huggingface(generation_prompt)
        elif request.ai_provider == AIProvider.GROK:
            response = await self._generate_with_grok(generation_prompt)
        else:
            raise ValueError(f"Unsupported AI provider: {request.ai_provider}")

        return self._parse_generation_response(response)

    async def suggest_modifications(
        self,
        project_id: str,
        modification_prompt: str,
        current_files: Dict[str, str],
        ai_provider: AIProvider = AIProvider.OPENAI,
    ) -> List[ModificationSuggestion]:
        """Generate modification suggestions based on user prompt"""

        modification_analysis_prompt = self._create_modification_prompt(
            modification_prompt, current_files
        )

        if ai_provider == AIProvider.OPENAI:
            response = await self._analyze_with_openai(modification_analysis_prompt)
        elif ai_provider == AIProvider.HUGGINGFACE:
            response = await self._analyze_with_huggingface(
                modification_analysis_prompt
            )
        elif ai_provider == AIProvider.GROK:
            response = await self._analyze_with_grok(modification_analysis_prompt)
        else:
            response = await self._analyze_with_openai(modification_analysis_prompt)

        return self._parse_modification_response(response, project_id)

    def _create_analysis_prompt(self, request: IntelligentProjectRequest) -> str:
        """Create prompt for project requirement analysis"""

        return f"""
You are an expert software architect and technology consultant. Analyze the following project requirements and provide a comprehensive technical analysis.

PROJECT PROMPT: {request.prompt}

USER PREFERENCES:
- Preferred Technologies: {request.preferred_technologies or 'None specified'}
- Target Platforms: {request.target_platform or 'Not specified'}
- Complexity Level: {request.complexity_level}
- Constraints: {request.constraints or 'None specified'}
- Include Tests: {request.include_tests}
- Include Documentation: {request.include_documentation}
- Include Deployment: {request.include_deployment}
- Include CI/CD: {request.include_ci_cd}

ANALYSIS GUIDELINES:
1. **Project Type Detection**:
   - If the prompt mentions "web app", "dashboard", "website", "UI", "frontend", or user interface → likely needs frontend
   - If the prompt mentions "API", "backend", "server", "database", "microservice" → likely needs backend
   - If the prompt mentions "full-stack", "complete app", or both UI and API features → fullstack
   - Default to fullstack for comprehensive applications

2. **Technology Stack Selection**:
   - Frontend: Prefer React + TypeScript + Vite for modern web apps
   - Backend: Prefer FastAPI + Python for APIs, Express.js for Node.js projects
   - Database: SQLite for simple projects, PostgreSQL for complex ones
   - Styling: Tailwind CSS for modern, responsive design

3. **Feature Analysis**: Extract specific features mentioned in the prompt and suggest additional complementary features

Please provide a detailed analysis in the following JSON format:
{{
    "project_type": "fullstack|frontend|backend|mobile|desktop|api|microservice",
    "suggested_name": "project-name-suggestion",
    "technology_stack": {{
        "frontend": ["React", "TypeScript", "Vite"],
        "backend": ["FastAPI", "Python"],
        "database": ["SQLite"],
        "deployment": ["Vercel", "Railway"],
        "testing": ["Vitest", "Pytest"],
        "styling": ["Tailwind CSS"],
        "state_management": ["React Context"],
        "authentication": ["JWT"],
        "additional_tools": ["Axios", "React Router"]
    }},
    "architecture_pattern": "MVC|microservices|serverless|monolithic|jamstack",
    "estimated_complexity": "simple|medium|complex|enterprise",
    "key_features": ["feature1", "feature2", "feature3"],
    "technical_requirements": ["requirement1", "requirement2"],
    "suggested_folder_structure": {{
        "frontend": {{"src": {{"components": {{}}, "pages": {{}}, "services": {{}}}},
        "backend": {{"src": {{"controllers": {{}}, "models": {{}}, "services": {{}}}},
        "shared": {{"types": {{}}, "utils": {{}}}}
    }},
    "dependencies": {{
        "frontend": ["react", "typescript", "vite", "tailwindcss", "axios", "react-router-dom"],
        "backend": ["fastapi", "uvicorn", "sqlalchemy", "pydantic"],
        "devDependencies": ["vitest", "eslint", "prettier"]
    }},
    "reasoning": "Detailed explanation of technology choices and architecture decisions"
}}

CRITICAL REQUIREMENTS:
1. Always include both frontend AND backend for fullstack projects
2. Ensure technology choices match the project requirements
3. Consider the user's preferred technologies when specified
4. Provide realistic complexity assessment
5. Include comprehensive feature list based on the prompt
6. Explain your reasoning clearly

Focus on:
1. Understanding the core business requirements from the prompt
2. Detecting whether frontend, backend, or both are needed
3. Selecting the most appropriate technology stack
4. Considering scalability and maintainability
5. Balancing complexity with user preferences
6. Providing clear reasoning for all decisions
"""

    def _create_generation_prompt(
        self, analysis: ProjectAnalysis, request: IntelligentProjectRequest
    ) -> str:
        """Create prompt for project code generation"""

        tech_stack = analysis.technology_stack
        project_type = analysis.project_type

        # Determine what needs to be generated
        needs_frontend = (
            project_type in ["fullstack", "frontend"] or tech_stack.frontend
        )
        needs_backend = (
            project_type in ["fullstack", "backend", "api"] or tech_stack.backend
        )

        return f"""
You are an expert full-stack developer. Generate a complete, production-ready project based on the following analysis.

PROJECT ANALYSIS:
- Project Type: {project_type}
- Project Name: {analysis.suggested_name}
- Technology Stack: {tech_stack.model_dump_json()}
- Architecture: {analysis.architecture_pattern}
- Key Features: {', '.join(analysis.key_features)}
- Needs Frontend: {needs_frontend}
- Needs Backend: {needs_backend}

ORIGINAL REQUEST: {request.prompt}

CRITICAL GENERATION REQUIREMENTS:
1. **Frontend Generation** (Required: {needs_frontend}):
   {f"- Generate React + TypeScript frontend in 'frontend/' directory" if needs_frontend else "- No frontend needed"}
   {f"- Include components, pages, routing, and styling" if needs_frontend else ""}
   {f"- Use {', '.join(tech_stack.frontend)} technologies" if needs_frontend and tech_stack.frontend else ""}

2. **Backend Generation** (Required: {needs_backend}):
   {f"- Generate FastAPI backend in 'backend/' directory" if needs_backend else "- No backend needed"}
   {f"- Include API endpoints, models, and database setup" if needs_backend else ""}
   {f"- Use {', '.join(tech_stack.backend)} technologies" if needs_backend and tech_stack.backend else ""}

3. **Project Structure**:
   - Root README.md with setup instructions
   - Separate frontend/ and backend/ directories if both are needed
   - Configuration files for each part
   - Environment setup files

CRITICAL: You MUST return a valid JSON object with the exact structure below. Do not include any text before or after the JSON.

Generate a complete project with ALL necessary files. Include:
1. Configuration files (package.json, requirements.txt, etc.)
2. Main application files with complete implementation
3. Database models and schemas (if backend needed)
4. API endpoints and routes (if backend needed)
5. Frontend components and pages (if frontend needed)
6. Styling files and UI components (if frontend needed)
7. Test files and configurations
8. Environment configuration files
9. Documentation (README.md)
10. Deployment configurations

Return ONLY this JSON structure:
{{
    "files": {{
        "README.md": "# {analysis.suggested_name}\\n\\nComplete project documentation...",
        {"frontend/package.json" if needs_frontend else "package.json"}: "Frontend package.json with dependencies...",
        {"backend/requirements.txt" if needs_backend else "requirements.txt"}: "Backend requirements...",
        {"frontend/src/App.tsx" if needs_frontend else "src/App.js"}: "Main App component...",
        {"backend/main.py" if needs_backend else "main.py"}: "FastAPI main application..."
    }},
    "instructions": "Setup instructions for the generated project",
    "next_steps": ["Install dependencies", "Configure environment", "Start servers"],
    "additional_notes": "Project generated with {project_type} architecture"
}}

REQUIREMENTS:
- Generate COMPLETE, working code (not placeholders)
- Include ALL necessary files for a working project
- Follow best practices for the chosen technology stack
- Add proper error handling and validation
- Include comprehensive documentation
- Ensure code is production-ready
- Generate at least 15-20 files for fullstack projects
- Include proper configuration files for each technology
- Create proper folder structure (frontend/, backend/ if both needed)

RESPOND WITH ONLY THE JSON OBJECT - NO OTHER TEXT!
"""

    def _create_modification_prompt(
        self, modification_prompt: str, current_files: Dict[str, str]
    ) -> str:
        """Create prompt for project modifications"""

        files_summary = self._summarize_current_files(current_files)

        return f"""
You are an expert software developer. Analyze the following modification request for an existing project and suggest specific changes.

MODIFICATION REQUEST: {modification_prompt}

CURRENT PROJECT STRUCTURE:
{files_summary}

Analyze the request and provide modification suggestions in the following JSON format:
{{
    "suggestions": [
        {{
            "suggestion_id": "unique-id",
            "title": "Brief title of the modification",
            "description": "Detailed description of what will be changed",
            "category": "feature|bugfix|optimization|refactor",
            "files": [
                {{
                    "file_path": "path/to/file.ext",
                    "action": "create|modify|delete",
                    "content": "complete new/modified file content",
                    "reason": "why this change is needed",
                    "priority": "low|medium|high|critical"
                }}
            ],
            "estimated_effort": "small|medium|large",
            "impact": "low|medium|high",
            "reasoning": "detailed explanation of the changes"
        }}
    ]
}}

Focus on:
1. Understanding the exact requirements
2. Minimizing breaking changes
3. Following existing code patterns
4. Maintaining code quality
5. Providing complete, working implementations
"""

    def _summarize_current_files(self, files: Dict[str, str]) -> str:
        """Create a summary of current project files"""
        summary = []
        for file_path, content in files.items():
            lines = len(content.split("\n"))
            summary.append(f"- {file_path} ({lines} lines)")
        return "\n".join(summary[:20])  # Limit to first 20 files

    async def _analyze_with_openai(self, prompt: str) -> str:
        """Analyze using OpenAI"""
        try:
            chat = ChatOpenAI(
                openai_api_key=self.openai_api_key,
                model_name="gpt-4-1106-preview",
                temperature=0.1,
                max_tokens=4000,
            )

            messages = [
                SystemMessage(
                    content="You are an expert software architect and full-stack developer. You generate complete, production-ready code and project structures. Always return valid JSON when requested."
                ),
                HumanMessage(content=prompt),
            ]

            response = await chat.agenerate([messages])
            return response.generations[0][0].text
        except Exception as e:
            raise Exception(f"OpenAI analysis failed: {str(e)}")

    async def _analyze_with_huggingface(self, prompt: str) -> str:
        """Analyze using Hugging Face"""
        try:
            # Using Hugging Face Inference API with code-generation models
            headers = {"Authorization": f"Bearer {self.huggingface_api_key}"}

            # Try multiple models for better code generation
            models_to_try = [
                "microsoft/CodeGPT-small-py",  # Code generation model
                "microsoft/DialoGPT-large",  # Conversational model
                "bigcode/starcoder",  # Code-specific model
            ]

            for model in models_to_try:
                try:
                    api_url = f"https://api-inference.huggingface.co/models/{model}"
                    payload = {
                        "inputs": prompt,
                        "parameters": {
                            "max_length": 2000,
                            "temperature": 0.3,
                            "do_sample": True,
                        },
                    }

                    response = requests.post(
                        api_url, headers=headers, json=payload, timeout=30
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if isinstance(result, list) and len(result) > 0:
                            generated_text = result[0].get("generated_text", "")
                            if generated_text and len(generated_text) > len(prompt):
                                return generated_text
                        elif isinstance(result, dict) and "generated_text" in result:
                            return result["generated_text"]
                except Exception:
                    continue  # Try next model

            # If all Hugging Face models fail, fallback to OpenAI
            return await self._analyze_with_openai(prompt)

        except Exception as e:
            # Fallback to OpenAI if Hugging Face fails
            return await self._analyze_with_openai(prompt)

    async def _generate_with_openai(self, prompt: str) -> str:
        """Generate using OpenAI"""
        return await self._analyze_with_openai(prompt)

    async def _generate_with_huggingface(self, prompt: str) -> str:
        """Generate using Hugging Face"""
        return await self._analyze_with_huggingface(prompt)

    async def _analyze_with_grok(self, prompt: str) -> str:
        """Analyze using Grok"""
        try:
            # Using Grok API (X.AI)
            headers = {
                "Authorization": f"Bearer {self.grok_api_key}",
                "Content-Type": "application/json",
            }

            # Grok API endpoint (this is a placeholder - update with actual endpoint when available)
            api_url = "https://api.x.ai/v1/chat/completions"

            payload = {
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert software architect and developer.",
                    },
                    {"role": "user", "content": prompt},
                ],
                "model": "grok-beta",
                "temperature": 0.3,
                "max_tokens": 4000,
            }

            response = requests.post(api_url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"]
                else:
                    # Fallback to OpenAI if Grok fails
                    return await self._analyze_with_openai(prompt)
            else:
                # Fallback to OpenAI if Grok fails
                return await self._analyze_with_openai(prompt)

        except Exception as e:
            # Fallback to OpenAI if Grok fails
            return await self._analyze_with_openai(prompt)

    async def _generate_with_grok(self, prompt: str) -> str:
        """Generate using Grok"""
        return await self._analyze_with_grok(prompt)

    def _parse_analysis_response(
        self, response: str, request: IntelligentProjectRequest
    ) -> ProjectAnalysis:
        """Parse AI analysis response into ProjectAnalysis model"""
        try:
            # Extract JSON from response
            json_match = re.search(r"\{.*\}", response, re.DOTALL)
            if json_match:
                analysis_data = json.loads(json_match.group())

                # Create TechnologyStack
                tech_stack_data = analysis_data.get("technology_stack", {})
                technology_stack = TechnologyStack(**tech_stack_data)

                # Create ProjectAnalysis
                return ProjectAnalysis(
                    project_type=analysis_data.get("project_type", "fullstack"),
                    suggested_name=analysis_data.get(
                        "suggested_name", request.project_name or "ai-generated-project"
                    ),
                    technology_stack=technology_stack,
                    architecture_pattern=analysis_data.get(
                        "architecture_pattern", "MVC"
                    ),
                    estimated_complexity=analysis_data.get(
                        "estimated_complexity", request.complexity_level
                    ),
                    key_features=analysis_data.get("key_features", []),
                    technical_requirements=analysis_data.get(
                        "technical_requirements", []
                    ),
                    suggested_folder_structure=analysis_data.get(
                        "suggested_folder_structure", {}
                    ),
                    dependencies=analysis_data.get("dependencies", {}),
                    reasoning=analysis_data.get("reasoning", "AI analysis completed"),
                )
            else:
                raise ValueError("No valid JSON found in response")

        except Exception as e:
            # Fallback analysis
            return ProjectAnalysis(
                project_type="fullstack",
                suggested_name=request.project_name or "ai-generated-project",
                technology_stack=TechnologyStack(
                    frontend=["React", "TypeScript"],
                    backend=["FastAPI", "Python"],
                    database=["PostgreSQL"],
                ),
                architecture_pattern="MVC",
                estimated_complexity=request.complexity_level,
                key_features=["User Authentication", "API Integration"],
                technical_requirements=["Modern web standards", "Responsive design"],
                suggested_folder_structure={},
                dependencies={
                    "frontend": ["react", "typescript"],
                    "backend": ["fastapi"],
                },
                reasoning=f"Fallback analysis due to parsing error: {str(e)}",
            )

    def _parse_generation_response(self, response: str) -> Dict[str, Any]:
        """Parse AI generation response"""
        print(f"DEBUG: Parsing generation response, length: {len(response)}")
        print(f"DEBUG: Response contains 'files': {'files' in response}")
        print(f"DEBUG: Response contains 'instructions': {response}")

        try:
            # Try to find JSON in the response
            json_match = re.search(r"\{.*\}", response, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                print(f"DEBUG: Found JSON, length: {len(json_str)}")
                parsed_data = json.loads(json_str)

                # Validate that we have files
                if "files" in parsed_data and isinstance(parsed_data["files"], dict):
                    file_count = len(parsed_data["files"])
                    print(f"DEBUG: Successfully parsed {file_count} files")

                    # If we only have README.md, this might be a fallback response
                    if file_count == 1 and "README.md" in parsed_data["files"]:
                        print(
                            "WARNING: Only README.md found, generating additional files"
                        )
                        return self._generate_fallback_project_structure(response)

                    return parsed_data
                else:
                    print("WARNING: No valid files structure found in JSON")
                    return self._generate_fallback_project_structure(response)
            else:
                print("WARNING: No JSON found in response")
                return self._generate_fallback_project_structure(response)

        except json.JSONDecodeError as e:
            print(f"WARNING: JSON parsing failed: {e}")
            return self._generate_fallback_project_structure(response)
        except Exception as e:
            print(f"ERROR: Unexpected error in parsing: {e}")
            return self._generate_fallback_project_structure(response)

    def _generate_fallback_project_structure(self, ai_response: str) -> Dict[str, Any]:
        """Generate a complete project structure when AI response is incomplete"""
        print("DEBUG: Generating fallback project structure")

        # Create a basic but complete project structure
        files = {
            "README.md": f"""# AI Generated Project

## Description
{ai_response[:500] if ai_response else 'AI-generated project with modern web technologies.'}

## Features
- Modern web application
- Responsive design
- API integration ready
- Development environment configured

## Setup Instructions
1. Install dependencies: `npm install`
2. Start development server: `npm run dev`
3. Open http://localhost:3000

## Project Structure
- `src/` - Source code
- `public/` - Static assets
- `components/` - React components
- `services/` - API services
- `styles/` - CSS styles

## Technologies Used
- React 18
- Vite
- TypeScript
- Tailwind CSS
- Axios for API calls

Generated by AI Project Generator
""",
            "package.json": """{
  "name": "ai-generated-project",
  "version": "1.0.0",
  "type": "module",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext .js,.jsx,.ts,.tsx",
    "test": "vitest"
  },
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.6.0",
    "react-router-dom": "^6.8.0"
  },
  "devDependencies": {
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "autoprefixer": "^10.4.14",
    "eslint": "^8.38.0",
    "postcss": "^8.4.24",
    "tailwindcss": "^3.3.0",
    "typescript": "^5.0.2",
    "vite": "^4.3.0",
    "vitest": "^0.30.0"
  }
}""",
            "vite.config.ts": """import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})""",
            "index.html": """<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Generated Project</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>""",
            "src/main.tsx": """import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)""",
            "src/App.tsx": """import React, { useState, useEffect } from 'react'
import Header from './components/Header'
import MainContent from './components/MainContent'
import Footer from './components/Footer'

function App() {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setData({ message: 'Welcome to your AI-generated project!' })
      setLoading(false)
    }, 1000)
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <Header />
      <main className="flex-1">
        <MainContent data={data} loading={loading} />
      </main>
      <Footer />
    </div>
  )
}

export default App""",
            "src/components/Header.tsx": """import React from 'react'

const Header: React.FC = () => {
  return (
    <header className="bg-blue-600 text-white shadow-lg">
      <div className="container mx-auto px-4 py-6">
        <h1 className="text-3xl font-bold">AI Generated Project</h1>
        <p className="text-blue-100 mt-2">Built with modern web technologies</p>
      </div>
    </header>
  )
}

export default Header""",
            "src/components/MainContent.tsx": """import React from 'react'

interface MainContentProps {
  data: any
  loading: boolean
}

const MainContent: React.FC<MainContentProps> = ({ data, loading }) => {
  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-semibold mb-4">Welcome!</h2>
        <p className="text-gray-600 mb-4">{data?.message}</p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800">Feature 1</h3>
            <p className="text-blue-600 text-sm mt-2">Modern React components with TypeScript</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-800">Feature 2</h3>
            <p className="text-green-600 text-sm mt-2">Responsive design with Tailwind CSS</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="font-semibold text-purple-800">Feature 3</h3>
            <p className="text-purple-600 text-sm mt-2">Ready for API integration</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MainContent""",
            "src/components/Footer.tsx": """import React from 'react'

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-800 text-white py-6">
      <div className="container mx-auto px-4 text-center">
        <p>&copy; 2024 AI Generated Project. Built with ❤️ and AI.</p>
      </div>
    </footer>
  )
}

export default Footer""",
            "src/index.css": """@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}""",
            "tailwind.config.js": """/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}""",
            "postcss.config.js": """export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}""",
            "tsconfig.json": """{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}""",
            "tsconfig.node.json": """{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  },
  "include": ["vite.config.ts"]
}""",
            ".gitignore": """# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local""",
            ".eslintrc.cjs": """module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    'eslint:recommended',
    '@typescript-eslint/recommended',
    '@typescript-eslint/recommended-requiring-type-checking',
  ],
  ignorePatterns: ['dist', '.eslintrc.cjs'],
  parser: '@typescript-eslint/parser',
  plugins: ['react-refresh'],
  rules: {
    'react-refresh/only-export-components': [
      'warn',
      { allowConstantExport: true },
    ],
  },
}""",
        }

        return {
            "files": files,
            "instructions": """Setup Instructions:
1. Install dependencies: npm install
2. Start development server: npm run dev
3. Open http://localhost:3000 in your browser

The project includes:
- React 18 with TypeScript
- Vite for fast development
- Tailwind CSS for styling
- ESLint for code quality
- Responsive design components""",
            "next_steps": [
                "Install dependencies with npm install",
                "Start development server with npm run dev",
                "Customize components in src/components/",
                "Add your business logic",
                "Configure API endpoints",
                "Deploy to your preferred platform",
            ],
            "additional_notes": "This is a complete, production-ready React project with modern tooling and best practices. All files are functional and ready to use.",
        }

    def _parse_modification_response(
        self, response: str, project_id: str
    ) -> List[ModificationSuggestion]:
        """Parse AI modification response"""
        try:
            json_match = re.search(r"\{.*\}", response, re.DOTALL)
            if json_match:
                data = json.loads(json_match.group())
                suggestions = []

                for suggestion_data in data.get("suggestions", []):
                    files = []
                    for file_data in suggestion_data.get("files", []):
                        files.append(FileModification(**file_data))

                    suggestion = ModificationSuggestion(
                        suggestion_id=suggestion_data.get(
                            "suggestion_id", str(uuid.uuid4())
                        ),
                        title=suggestion_data.get("title", "Modification"),
                        description=suggestion_data.get("description", ""),
                        category=suggestion_data.get("category", "feature"),
                        files=files,
                        estimated_effort=suggestion_data.get(
                            "estimated_effort", "medium"
                        ),
                        impact=suggestion_data.get("impact", "medium"),
                        reasoning=suggestion_data.get("reasoning", ""),
                    )
                    suggestions.append(suggestion)

                return suggestions
            else:
                return []
        except Exception as e:
            return [
                ModificationSuggestion(
                    suggestion_id=str(uuid.uuid4()),
                    title="Parsing Error",
                    description=f"Failed to parse modification suggestions: {str(e)}",
                    category="bugfix",
                    files=[],
                    estimated_effort="small",
                    impact="low",
                    reasoning="Error in response parsing",
                )
            ]
