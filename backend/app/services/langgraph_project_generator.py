import os
import json
import uuid
from typing import Dict, Any, List, TypedDict, Annotated
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolExecutor
from langgraph.graph.message import add_messages

from app.models.project_models import (
    IntelligentProjectRequest,
    ProjectAnalysis,
    TechnologyStack,
)


class ProjectGenerationState(TypedDict):
    """State for the project generation workflow"""

    messages: Annotated[List, add_messages]
    request: IntelligentProjectRequest
    analysis: ProjectAnalysis
    project_structure: Dict[str, Any]
    generated_files: Dict[str, str]
    validation_results: Dict[str, Any]
    current_step: str
    progress: int
    error_message: str


class LangGraphProjectGenerator:
    """Enhanced project generator using LangGraph for workflow orchestration"""

    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.llm = ChatOpenAI(
            api_key=self.openai_api_key, model="gpt-4-1106-preview", temperature=0.1
        )
        self.workflow = self._create_workflow()

    def _create_workflow(self) -> StateGraph:
        """Create the LangGraph workflow for project generation"""
        workflow = StateGraph(ProjectGenerationState)

        # Add nodes
        workflow.add_node("analyze_requirements", self._analyze_requirements)
        workflow.add_node("design_architecture", self._design_architecture)
        workflow.add_node("generate_structure", self._generate_structure)
        workflow.add_node("generate_frontend", self._generate_frontend)
        workflow.add_node("generate_backend", self._generate_backend)
        workflow.add_node("generate_config", self._generate_config)
        workflow.add_node("validate_project", self._validate_project)
        workflow.add_node("finalize_project", self._finalize_project)

        # Define the workflow
        workflow.set_entry_point("analyze_requirements")

        workflow.add_edge("analyze_requirements", "design_architecture")
        workflow.add_edge("design_architecture", "generate_structure")
        workflow.add_edge("generate_structure", "generate_frontend")
        workflow.add_edge("generate_frontend", "generate_backend")
        workflow.add_edge("generate_backend", "generate_config")
        workflow.add_edge("generate_config", "validate_project")
        workflow.add_edge("validate_project", "finalize_project")
        workflow.add_edge("finalize_project", END)

        return workflow.compile()

    async def generate_project(
        self, request: IntelligentProjectRequest
    ) -> Dict[str, Any]:
        """Generate a complete project using the LangGraph workflow"""

        initial_state = ProjectGenerationState(
            messages=[],
            request=request,
            analysis=None,
            project_structure={},
            generated_files={},
            validation_results={},
            current_step="Starting project generation",
            progress=0,
            error_message="",
        )

        # Run the workflow
        final_state = await self.workflow.ainvoke(initial_state)

        return {
            "files": final_state["generated_files"],
            "analysis": final_state["analysis"],
            "structure": final_state["project_structure"],
            "validation": final_state["validation_results"],
            "instructions": self._generate_setup_instructions(final_state["analysis"]),
            "next_steps": self._generate_next_steps(final_state["analysis"]),
        }

    async def _analyze_requirements(
        self, state: ProjectGenerationState
    ) -> ProjectGenerationState:
        """Analyze project requirements from the user prompt"""

        state["current_step"] = "Analyzing project requirements"
        state["progress"] = 10

        analysis_prompt = f"""
        You are an expert software architect. Analyze this project request and provide a comprehensive technical analysis.
        
        PROJECT REQUEST: {state["request"].prompt}
        
        USER PREFERENCES:
        - Preferred Technologies: {state["request"].preferred_technologies or 'None'}
        - Target Platforms: {state["request"].target_platform or 'Not specified'}
        - Complexity Level: {state["request"].complexity_level}
        - Include Tests: {state["request"].include_tests}
        - Include Documentation: {state["request"].include_documentation}
        
        Provide analysis in JSON format with:
        - project_type (fullstack/frontend/backend/mobile/api)
        - suggested_name
        - technology_stack (frontend, backend, database, styling, testing)
        - architecture_pattern
        - estimated_complexity
        - key_features (list)
        - technical_requirements (list)
        - dependencies
        - reasoning
        
        Return ONLY valid JSON.
        """

        messages = [
            SystemMessage(
                content="You are an expert software architect providing technical analysis."
            ),
            HumanMessage(content=analysis_prompt),
        ]

        response = await self.llm.ainvoke(messages)

        try:
            analysis_data = json.loads(response.content)

            # Create TechnologyStack
            tech_stack_data = analysis_data.get("technology_stack", {})
            technology_stack = TechnologyStack(**tech_stack_data)

            # Create ProjectAnalysis
            analysis = ProjectAnalysis(
                project_type=analysis_data.get("project_type", "fullstack"),
                suggested_name=analysis_data.get(
                    "suggested_name", state["request"].project_name or "ai-project"
                ),
                technology_stack=technology_stack,
                architecture_pattern=analysis_data.get("architecture_pattern", "MVC"),
                estimated_complexity=analysis_data.get(
                    "estimated_complexity", state["request"].complexity_level
                ),
                key_features=analysis_data.get("key_features", []),
                technical_requirements=analysis_data.get("technical_requirements", []),
                suggested_folder_structure=analysis_data.get(
                    "suggested_folder_structure", {}
                ),
                dependencies=analysis_data.get("dependencies", {}),
                reasoning=analysis_data.get("reasoning", "AI analysis completed"),
            )

            state["analysis"] = analysis
            state["messages"].append(
                HumanMessage(content=f"Analysis completed: {analysis.suggested_name}")
            )

        except Exception as e:
            # Fallback analysis
            state["analysis"] = ProjectAnalysis(
                project_type="fullstack",
                suggested_name=state["request"].project_name or "ai-project",
                technology_stack=TechnologyStack(
                    frontend=["React", "TypeScript", "Vite"],
                    backend=["FastAPI", "Python"],
                    database=["SQLite"],
                    styling=["Tailwind CSS"],
                ),
                architecture_pattern="MVC",
                estimated_complexity=state["request"].complexity_level,
                key_features=["Modern UI", "API Integration"],
                technical_requirements=["Responsive design"],
                suggested_folder_structure={},
                dependencies={
                    "frontend": ["react", "typescript"],
                    "backend": ["fastapi"],
                },
                reasoning=f"Fallback analysis due to error: {str(e)}",
            )
            state["error_message"] = f"Analysis error (using fallback): {str(e)}"

        return state

    async def _design_architecture(
        self, state: ProjectGenerationState
    ) -> ProjectGenerationState:
        """Design the project architecture and folder structure"""

        state["current_step"] = "Designing project architecture"
        state["progress"] = 20

        analysis = state["analysis"]

        # Determine project structure based on analysis
        needs_frontend = (
            analysis.project_type in ["fullstack", "frontend"]
            or analysis.technology_stack.frontend
        )
        needs_backend = (
            analysis.project_type in ["fullstack", "backend", "api"]
            or analysis.technology_stack.backend
        )

        project_structure = {
            "type": analysis.project_type,
            "name": analysis.suggested_name,
            "needs_frontend": needs_frontend,
            "needs_backend": needs_backend,
            "architecture": analysis.architecture_pattern,
            "folders": {},
        }

        if needs_frontend:
            project_structure["folders"]["frontend"] = {
                "src": ["components", "pages", "services", "hooks", "utils", "types"],
                "public": ["assets", "icons"],
                "config": ["vite.config.ts", "tailwind.config.js", "tsconfig.json"],
            }

        if needs_backend:
            project_structure["folders"]["backend"] = {
                "app": ["models", "services", "routes", "utils", "middleware"],
                "tests": ["unit", "integration"],
                "config": ["requirements.txt", "main.py", ".env.example"],
            }

        # Add root level files
        project_structure["folders"]["root"] = [
            "README.md",
            ".gitignore",
            "docker-compose.yml",
        ]

        state["project_structure"] = project_structure
        state["messages"].append(
            HumanMessage(
                content=f"Architecture designed: {analysis.architecture_pattern}"
            )
        )

        return state

    async def _generate_structure(
        self, state: ProjectGenerationState
    ) -> ProjectGenerationState:
        """Generate the basic project structure and configuration files"""

        state["current_step"] = "Generating project structure"
        state["progress"] = 30

        analysis = state["analysis"]
        structure = state["project_structure"]
        files = {}

        # Generate README.md
        files["README.md"] = self._generate_readme(analysis)

        # Generate .gitignore
        files[".gitignore"] = self._generate_gitignore(structure)

        # Generate docker-compose.yml if needed
        if structure["needs_frontend"] and structure["needs_backend"]:
            files["docker-compose.yml"] = self._generate_docker_compose(analysis)

        state["generated_files"].update(files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(files)} structure files")
        )

        return state

    async def _generate_frontend(
        self, state: ProjectGenerationState
    ) -> ProjectGenerationState:
        """Generate frontend files and components"""

        state["current_step"] = "Generating frontend components"
        state["progress"] = 50

        analysis = state["analysis"]
        structure = state["project_structure"]

        if not structure["needs_frontend"]:
            return state

        # Generate fallback frontend files
        frontend_files = self._generate_fallback_frontend(analysis)
        state["generated_files"].update(frontend_files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(frontend_files)} frontend files")
        )

        return state

    async def _generate_backend(
        self, state: ProjectGenerationState
    ) -> ProjectGenerationState:
        """Generate backend files and API endpoints"""

        state["current_step"] = "Generating backend API"
        state["progress"] = 70

        analysis = state["analysis"]
        structure = state["project_structure"]

        if not structure["needs_backend"]:
            return state

        # Generate fallback backend files
        backend_files = self._generate_fallback_backend(analysis)
        state["generated_files"].update(backend_files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(backend_files)} backend files")
        )

        return state

    async def _generate_config(
        self, state: ProjectGenerationState
    ) -> ProjectGenerationState:
        """Generate configuration and deployment files"""

        state["current_step"] = "Generating configuration files"
        state["progress"] = 85

        analysis = state["analysis"]
        structure = state["project_structure"]

        config_files = {}

        # Generate deployment configurations
        if structure["needs_frontend"] and structure["needs_backend"]:
            config_files["Makefile"] = self._generate_makefile(analysis)

        # Generate CI/CD if requested
        if state["request"].include_ci_cd:
            config_files[".github/workflows/ci.yml"] = self._generate_github_actions(
                analysis
            )

        # Generate environment files
        config_files[".env.example"] = self._generate_env_example(analysis)

        state["generated_files"].update(config_files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(config_files)} config files")
        )

        return state

    async def _validate_project(
        self, state: ProjectGenerationState
    ) -> ProjectGenerationState:
        """Validate the generated project structure and files"""

        state["current_step"] = "Validating project structure"
        state["progress"] = 95

        validation_results = {
            "total_files": len(state["generated_files"]),
            "has_frontend": any(
                path.startswith("frontend/") for path in state["generated_files"]
            ),
            "has_backend": any(
                path.startswith("backend/") for path in state["generated_files"]
            ),
            "has_readme": "README.md" in state["generated_files"],
            "has_gitignore": ".gitignore" in state["generated_files"],
            "missing_files": [],
            "validation_passed": True,
        }

        # Check for essential files
        essential_files = ["README.md"]

        if state["project_structure"]["needs_frontend"]:
            essential_files.extend(
                [
                    "frontend/package.json",
                    "frontend/src/App.tsx",
                    "frontend/src/main.tsx",
                ]
            )

        if state["project_structure"]["needs_backend"]:
            essential_files.extend(["backend/requirements.txt", "backend/main.py"])

        for file in essential_files:
            if file not in state["generated_files"]:
                validation_results["missing_files"].append(file)
                validation_results["validation_passed"] = False

        state["validation_results"] = validation_results
        state["messages"].append(
            HumanMessage(
                content=f"Validation completed: {validation_results['validation_passed']}"
            )
        )

        return state

    async def _finalize_project(
        self, state: ProjectGenerationState
    ) -> ProjectGenerationState:
        """Finalize the project generation"""

        state["current_step"] = "Project generation completed"
        state["progress"] = 100

        # Add final summary
        summary = {
            "project_name": state["analysis"].suggested_name,
            "project_type": state["analysis"].project_type,
            "total_files": len(state["generated_files"]),
            "technologies": {
                "frontend": state["analysis"].technology_stack.frontend,
                "backend": state["analysis"].technology_stack.backend,
                "database": state["analysis"].technology_stack.database,
            },
            "validation_passed": state["validation_results"].get(
                "validation_passed", False
            ),
        }

        state["messages"].append(
            HumanMessage(content=f"Project generation completed: {summary}")
        )

        return state

    # Helper methods for generating file content

    def _generate_readme(self, analysis: ProjectAnalysis) -> str:
        """Generate README.md content"""
        return f"""# {analysis.suggested_name}

## Description
{analysis.reasoning}

## Features
{chr(10).join(f"- {feature}" for feature in analysis.key_features)}

## Technology Stack
- **Frontend**: {', '.join(analysis.technology_stack.frontend or [])}
- **Backend**: {', '.join(analysis.technology_stack.backend or [])}
- **Database**: {', '.join(analysis.technology_stack.database or [])}
- **Styling**: {', '.join(analysis.technology_stack.styling or [])}

## Architecture
- **Pattern**: {analysis.architecture_pattern}
- **Complexity**: {analysis.estimated_complexity}

## Setup Instructions

### Prerequisites
- Node.js 18+ (for frontend)
- Python 3.9+ (for backend)
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd {analysis.suggested_name}
```

2. Install frontend dependencies:
```bash
cd frontend
npm install
```

3. Install backend dependencies:
```bash
cd ../backend
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

### Running the Application

1. Start the backend server:
```bash
cd backend
python main.py
```

2. Start the frontend development server:
```bash
cd frontend
npm run dev
```

3. Open your browser and navigate to `http://localhost:3000`

## Project Structure
```
{analysis.suggested_name}/
├── frontend/          # React frontend application
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── services/
│   └── package.json
├── backend/           # FastAPI backend application
│   ├── app/
│   │   ├── models/
│   │   ├── routes/
│   │   └── services/
│   └── requirements.txt
└── README.md
```

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License
MIT License

---
Generated by AI Project Generator
"""

    def _generate_gitignore(self, structure: Dict[str, Any]) -> str:
        """Generate .gitignore content"""
        gitignore_content = """# Dependencies
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
*.tsbuildinfo

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
"""

        if structure.get("needs_frontend"):
            gitignore_content += """
# Frontend specific
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/
"""

        if structure.get("needs_backend"):
            gitignore_content += """
# Backend specific
backend/__pycache__/
backend/*.pyc
backend/.pytest_cache/
backend/instance/
backend/.coverage
backend/htmlcov/
"""

        return gitignore_content

    def _generate_fallback_frontend(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate complete frontend files"""
        project_name = analysis.suggested_name

        return {
            "frontend/package.json": f"""{{"name": "{project_name}-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {{
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext .js,.jsx,.ts,.tsx",
    "test": "vitest"
  }},
  "dependencies": {{
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.6.0",
    "react-router-dom": "^6.8.0"
  }},
  "devDependencies": {{
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "autoprefixer": "^10.4.14",
    "eslint": "^8.38.0",
    "postcss": "^8.4.24",
    "tailwindcss": "^3.3.0",
    "typescript": "^5.0.2",
    "vite": "^4.3.0",
    "vitest": "^0.30.0"
  }}
}}""",
            "frontend/vite.config.ts": """import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: true
  }
})""",
            "frontend/index.html": f"""<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{project_name}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>""",
            "frontend/src/main.tsx": """import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)""",
            "frontend/src/App.tsx": f"""import React, {{ useState, useEffect }} from 'react'
import {{ BrowserRouter as Router, Routes, Route }} from 'react-router-dom'
import Header from './components/Header'
import MainContent from './components/MainContent'
import Footer from './components/Footer'

function App() {{
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {{
    // Simulate API call
    setTimeout(() => {{
      setData({{ message: 'Welcome to {project_name}!' }})
      setLoading(false)
    }}, 1000)
  }}, [])

  return (
    <Router>
      <div className="min-h-screen bg-gray-50 flex flex-col">
        <Header />
        <main className="flex-1">
          <Routes>
            <Route path="/" element={{<MainContent data={{data}} loading={{loading}} />}} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  )
}}

export default App""",
            "frontend/src/components/Header.tsx": f"""import React from 'react'
import {{ Link }} from 'react-router-dom'

const Header: React.FC = () => {{
  return (
    <header className="bg-blue-600 text-white shadow-lg">
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">{project_name}</h1>
            <p className="text-blue-100 mt-2">Built with React and TypeScript</p>
          </div>
          <nav>
            <Link to="/" className="text-blue-100 hover:text-white px-3 py-2 rounded-md">
              Home
            </Link>
          </nav>
        </div>
      </div>
    </header>
  )
}}

export default Header""",
            "frontend/src/components/MainContent.tsx": f"""import React from 'react'

interface MainContentProps {{
  data: any
  loading: boolean
}}

const MainContent: React.FC<MainContentProps> = ({{ data, loading }}) => {{
  if (loading) {{
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }}

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-semibold mb-4">Welcome to {project_name}!</h2>
        <p className="text-gray-600 mb-4">{{data?.message}}</p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
          <div className="bg-blue-50 p-4 rounded-lg">
            <h3 className="font-semibold text-blue-800">Modern Stack</h3>
            <p className="text-blue-600 text-sm mt-2">Built with React 18, TypeScript, and Vite</p>
          </div>
          <div className="bg-green-50 p-4 rounded-lg">
            <h3 className="font-semibold text-green-800">Responsive Design</h3>
            <p className="text-green-600 text-sm mt-2">Mobile-first design with Tailwind CSS</p>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <h3 className="font-semibold text-purple-800">API Ready</h3>
            <p className="text-purple-600 text-sm mt-2">Configured for backend integration</p>
          </div>
        </div>
      </div>
    </div>
  )
}}

export default MainContent""",
            "frontend/src/components/Footer.tsx": """import React from 'react'

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-800 text-white py-6">
      <div className="container mx-auto px-4 text-center">
        <p>&copy; 2024 AI Generated Project. Built with ❤️ and AI.</p>
      </div>
    </footer>
  )
}

export default Footer""",
            "frontend/src/index.css": """@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}""",
            "frontend/tailwind.config.js": """/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}""",
            "frontend/tsconfig.json": """{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}""",
            "frontend/postcss.config.js": """export default {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}""",
        }

    def _generate_fallback_backend(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate complete backend files"""
        project_name = analysis.suggested_name

        return {
            "backend/requirements.txt": """fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-multipart==0.0.6
python-dotenv==1.0.0
sqlalchemy==2.0.23
alembic==1.13.0
psycopg2-binary==2.9.9
pytest==7.4.3
httpx==0.25.2""",
            "backend/main.py": f"""import os
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from dotenv import load_dotenv
import uvicorn

# Load environment variables
load_dotenv()

# Create FastAPI app
app = FastAPI(
    title="{project_name} API",
    description="Backend API for {project_name}",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    return {{"message": "Welcome to {project_name} API!", "status": "running"}}

@app.get("/health")
async def health_check():
    return {{"status": "healthy", "service": "{project_name}"}}

@app.get("/api/data")
async def get_data():
    return {{
        "data": [
            {{"id": 1, "name": "Item 1", "description": "First item"}},
            {{"id": 2, "name": "Item 2", "description": "Second item"}},
            {{"id": 3, "name": "Item 3", "description": "Third item"}}
        ],
        "total": 3
    }}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )""",
            "backend/app/__init__.py": "",
            "backend/app/models.py": """from sqlalchemy import Column, Integer, String, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class BaseModel(Base):
    __abstract__ = True

    id = Column(Integer, primary_key=True, index=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

class Item(BaseModel):
    __tablename__ = "items"

    name = Column(String, index=True)
    description = Column(String)
    is_active = Column(Boolean, default=True)""",
            "backend/app/database.py": f"""import os
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

load_dotenv()

# Database URL
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./app.db")

# Create engine
engine = create_engine(
    DATABASE_URL,
    connect_args={{"check_same_thread": False}} if "sqlite" in DATABASE_URL else {{}}
)

# Create session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base class
Base = declarative_base()

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()""",
            "backend/app/schemas.py": """from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class ItemBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_active: bool = True

class ItemCreate(ItemBase):
    pass

class ItemUpdate(ItemBase):
    name: Optional[str] = None
    is_active: Optional[bool] = None

class Item(ItemBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True""",
            "backend/.env.example": f"""# {project_name} Environment Variables

# Database
DATABASE_URL=sqlite:///./app.db

# API Settings
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30""",
            "backend/Dockerfile": f"""FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8000

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]""",
        }

    def _generate_docker_compose(self, analysis: ProjectAnalysis) -> str:
        """Generate docker-compose.yml"""
        project_name = analysis.suggested_name.lower().replace(" ", "-")

        return f"""version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/{project_name}
    depends_on:
      - db
    volumes:
      - ./backend:/app

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB={project_name}
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
"""

    def _generate_makefile(self, analysis: ProjectAnalysis) -> str:
        """Generate Makefile for project management"""
        return """# Project Management Makefile

.PHONY: help install dev build test clean docker-up docker-down

help:
	@echo "Available commands:"
	@echo "  install     - Install all dependencies"
	@echo "  dev         - Start development servers"
	@echo "  build       - Build the project"
	@echo "  test        - Run tests"
	@echo "  clean       - Clean build artifacts"
	@echo "  docker-up   - Start with Docker Compose"
	@echo "  docker-down - Stop Docker Compose"

install:
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "Installing backend dependencies..."
	cd backend && pip install -r requirements.txt

dev:
	@echo "Starting development servers..."
	@echo "Backend will run on http://localhost:8000"
	@echo "Frontend will run on http://localhost:3000"
	@make -j2 dev-backend dev-frontend

dev-backend:
	cd backend && python main.py

dev-frontend:
	cd frontend && npm run dev

build:
	@echo "Building frontend..."
	cd frontend && npm run build
	@echo "Build complete!"

test:
	@echo "Running frontend tests..."
	cd frontend && npm test
	@echo "Running backend tests..."
	cd backend && python -m pytest

clean:
	@echo "Cleaning build artifacts..."
	rm -rf frontend/dist
	rm -rf frontend/build
	rm -rf backend/__pycache__
	rm -rf backend/.pytest_cache

docker-up:
	docker-compose up -d

docker-down:
	docker-compose down
"""

    def _generate_env_example(self, analysis: ProjectAnalysis) -> str:
        """Generate .env.example file"""
        return f"""# {analysis.suggested_name} Environment Variables

# Development
NODE_ENV=development
DEBUG=true

# API Configuration
API_URL=http://localhost:8000
API_TIMEOUT=30000

# Database
DATABASE_URL=sqlite:///./app.db

# Security
JWT_SECRET=your-jwt-secret-here
ENCRYPTION_KEY=your-encryption-key-here

# External Services
# Add your API keys and service configurations here
"""

    def _generate_github_actions(self, analysis: ProjectAnalysis) -> str:
        """Generate GitHub Actions CI/CD workflow"""
        return f"""name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci

    - name: Install backend dependencies
      run: |
        cd backend
        pip install -r requirements.txt

    - name: Run frontend tests
      run: |
        cd frontend
        npm test

    - name: Run backend tests
      run: |
        cd backend
        python -m pytest

    - name: Build frontend
      run: |
        cd frontend
        npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Add your deployment steps here"
        # Example: Deploy to your hosting service
"""

    def _generate_setup_instructions(self, analysis: ProjectAnalysis) -> List[str]:
        """Generate setup instructions for the project"""
        instructions = [
            "1. Clone or download the generated project files",
            "2. Navigate to the project directory",
            "3. Install frontend dependencies: cd frontend && npm install",
            "4. Install backend dependencies: cd backend && pip install -r requirements.txt",
            "5. Copy .env.example to .env and configure your environment variables",
            "6. Start the backend server: cd backend && python main.py",
            "7. Start the frontend development server: cd frontend && npm run dev",
            "8. Open your browser and navigate to http://localhost:3000",
        ]

        if (
            analysis.technology_stack.database
            and "postgresql" in str(analysis.technology_stack.database).lower()
        ):
            instructions.insert(
                5, "5a. Set up PostgreSQL database and update DATABASE_URL in .env"
            )

        return instructions

    def _generate_next_steps(self, analysis: ProjectAnalysis) -> List[str]:
        """Generate next steps for project development"""
        return [
            "🎯 Customize the UI components to match your design requirements",
            "🔧 Add your specific business logic to the backend API endpoints",
            "🗄️ Set up your database schema and models",
            "🔐 Implement authentication and authorization if needed",
            "📝 Add comprehensive tests for your components and API endpoints",
            "🚀 Configure deployment to your preferred hosting platform",
            "📚 Update documentation with your specific requirements",
            "🔍 Add monitoring and logging for production use",
        ]
