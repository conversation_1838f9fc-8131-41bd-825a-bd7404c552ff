import os
import uuid
import asyncio
import zipfile
import json
from typing import Dict, Any, Optional, List
from pathlib import Path

from app.models.project_models import (
    IntelligentProjectRequest,
    IntelligentProjectResponse,
    EnhancedGenerationStatus,
    ProjectAnalysis,
    ModificationRequest,
    ModificationResponse,
    ModificationSuggestion,
    ApplyModificationRequest,
)
from app.services.intelligent_ai_service import IntelligentAIService
from app.utils.file_utils import FileUtils

# Optional LangGraph import
try:
    from app.services.langgraph_project_generator import LangGraphProjectGenerator

    LANGGRAPH_AVAILABLE = True
except ImportError:
    LANGGRAPH_AVAILABLE = False
    LangGraphProjectGenerator = None


class IntelligentProjectGenerator:
    """Enhanced project generator with intelligent AI capabilities"""

    def __init__(self):
        self.ai_service = IntelligentAIService()
        self.file_utils = FileUtils()

        # Initialize LangGraph generator if available
        if LANGGRAPH_AVAILABLE and LangGraphProjectGenerator:
            try:
                self.langgraph_generator = LangGraphProjectGenerator()
            except Exception as e:
                print(f"Warning: Failed to initialize LangGraph generator: {e}")
                self.langgraph_generator = None
        else:
            self.langgraph_generator = None

        self.generation_status: Dict[str, EnhancedGenerationStatus] = {}
        self.project_analyses: Dict[str, ProjectAnalysis] = {}
        self.project_files: Dict[str, Dict[str, str]] = {}  # project_id -> files
        self.modification_suggestions: Dict[str, List[ModificationSuggestion]] = {}
        self.output_dir = Path("generated_projects")
        self.output_dir.mkdir(exist_ok=True)

    async def generate_intelligent_project(
        self, request: IntelligentProjectRequest
    ) -> IntelligentProjectResponse:
        """Generate project using intelligent AI analysis"""
        project_id = str(uuid.uuid4())

        # Initialize enhanced status tracking
        self.generation_status[project_id] = EnhancedGenerationStatus(
            project_id=project_id,
            status="analyzing",
            progress=0,
            current_step="Analyzing project requirements with AI",
            phase="analysis",
            can_modify=False,
        )

        try:
            # Start generation in background
            asyncio.create_task(
                self._generate_intelligent_project_async(project_id, request)
            )

            return IntelligentProjectResponse(
                project_id=project_id,
                status="analyzing",
                message="Intelligent project generation started successfully",
            )
        except Exception as e:
            self.generation_status[project_id].status = "failed"
            self.generation_status[project_id].error_message = str(e)

            return IntelligentProjectResponse(
                project_id=project_id,
                status="failed",
                message=f"Failed to start intelligent project generation: {str(e)}",
            )

    async def _generate_intelligent_project_async(
        self, project_id: str, request: IntelligentProjectRequest
    ):
        """Async intelligent project generation process"""
        try:
            status = self.generation_status[project_id]

            # Phase 1: AI Analysis
            status.current_step = "Analyzing project requirements with AI"
            status.progress = 10
            status.phase = "analysis"

            print(f"DEBUG: Starting AI analysis for project {project_id}")

            try:
                analysis = await self.ai_service.analyze_project_requirements(request)
                print(
                    f"DEBUG: Analysis completed successfully: {analysis.suggested_name}"
                )
            except Exception as analysis_error:
                print(f"ERROR: Analysis failed: {analysis_error}")
                # Create a fallback analysis
                from app.models.project_models import ProjectAnalysis, TechnologyStack

                analysis = ProjectAnalysis(
                    project_type="fullstack",
                    suggested_name=request.project_name or "ai-generated-project",
                    technology_stack=TechnologyStack(
                        frontend=["React", "TypeScript", "Vite"],
                        backend=["FastAPI", "Python"],
                        database=["SQLite"],
                        styling=["Tailwind CSS"],
                        testing=["Vitest", "Jest"],
                    ),
                    architecture_pattern="MVC",
                    estimated_complexity=request.complexity_level,
                    key_features=["Modern UI", "Responsive Design", "API Ready"],
                    technical_requirements=["Modern web standards"],
                    suggested_folder_structure={},
                    dependencies={
                        "frontend": ["react", "typescript", "vite", "tailwindcss"],
                        "backend": ["fastapi", "uvicorn"],
                    },
                    reasoning=f"Fallback analysis due to AI error: {str(analysis_error)}",
                )
                print(f"DEBUG: Using fallback analysis: {analysis.suggested_name}")

            self.project_analyses[project_id] = analysis
            status.analysis = analysis
            print(
                f"DEBUG: Analysis stored. Available analyses: {list(self.project_analyses.keys())}"
            )

            # Phase 2: Project Generation
            status.current_step = "Generating project structure and code"
            status.progress = 30
            status.phase = "generation"
            status.status = "generating"

            print(f"DEBUG: Starting enhanced code generation for project {project_id}")
            try:
                # Try LangGraph generator first if available
                if (
                    self.langgraph_generator
                    and LANGGRAPH_AVAILABLE
                    and os.getenv("OPENAI_API_KEY")
                ):
                    print("DEBUG: Using LangGraph generator")
                    ai_result = await self.langgraph_generator.generate_project(request)
                    print(
                        f"DEBUG: LangGraph generation completed with {len(ai_result.get('files', {}))} files"
                    )
                else:
                    # Use enhanced fallback generation
                    print("DEBUG: Using enhanced fallback generator")
                    ai_result = self._generate_enhanced_fallback_project_structure(
                        analysis, "Using enhanced fallback"
                    )
                    print(
                        f"DEBUG: Enhanced fallback generation with {len(ai_result.get('files', {}))} files"
                    )
            except Exception as generation_error:
                print(f"ERROR: Generation failed: {generation_error}")
                # Use enhanced fallback generation
                ai_result = self._generate_enhanced_fallback_project_structure(
                    analysis, str(generation_error)
                )
                print(
                    f"DEBUG: Using enhanced fallback generation with {len(ai_result.get('files', {}))} files"
                )

            # Phase 3: File Creation
            status.current_step = "Creating project files"
            status.progress = 60

            project_dir = self.output_dir / project_id
            project_dir.mkdir(exist_ok=True)

            generated_files = ai_result.get("files", {})
            self.project_files[project_id] = generated_files

            # Write files to disk atomically to minimize reload triggers
            import tempfile
            import shutil

            for file_path, content in generated_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)

                # Write to temporary file first, then move to final location
                # This reduces the chance of triggering file watchers during write
                with tempfile.NamedTemporaryFile(
                    mode="w",
                    encoding="utf-8",
                    delete=False,
                    dir=full_path.parent,
                    suffix=".tmp",
                ) as temp_file:
                    temp_file.write(content)
                    temp_file.flush()

                # Atomic move to final location
                shutil.move(temp_file.name, full_path)

            # Phase 4: Generate Initial Suggestions
            status.current_step = "Generating improvement suggestions"
            status.progress = 80
            status.phase = "review"

            # Generate initial modification suggestions
            initial_suggestions = await self._generate_initial_suggestions(
                project_id, analysis, generated_files
            )
            self.modification_suggestions[project_id] = initial_suggestions
            status.pending_modifications = initial_suggestions

            # Phase 5: Create ZIP file
            status.current_step = "Creating downloadable package"
            status.progress = 90

            zip_path = await self._create_zip_file(project_id, project_dir)

            # Phase 6: Complete
            status.status = "completed"
            status.progress = 100
            status.current_step = "Project generation completed"
            status.can_modify = True

        except Exception as e:
            status.status = "failed"
            status.error_message = str(e)
            status.current_step = f"Generation failed: {str(e)}"

    async def request_modifications(
        self, modification_request: ModificationRequest
    ) -> ModificationResponse:
        """Request modifications to an existing project"""
        project_id = modification_request.project_id
        modification_id = str(uuid.uuid4())

        if project_id not in self.project_files:
            raise ValueError("Project not found")

        try:
            current_files = self.project_files[project_id]
            suggestions = await self.ai_service.suggest_modifications(
                project_id,
                modification_request.modification_prompt,
                current_files,
                modification_request.ai_provider,
            )

            # Store suggestions
            if project_id not in self.modification_suggestions:
                self.modification_suggestions[project_id] = []

            self.modification_suggestions[project_id].extend(suggestions)

            # Update status
            if project_id in self.generation_status:
                self.generation_status[project_id].pending_modifications = (
                    self.modification_suggestions[project_id]
                )

            # Auto-apply if requested
            if modification_request.apply_immediately and suggestions:
                await self._apply_modifications(
                    project_id, [s.suggestion_id for s in suggestions]
                )
                status = "applied"
            else:
                status = "ready"

            return ModificationResponse(
                modification_id=modification_id,
                project_id=project_id,
                suggestions=suggestions,
                status=status,
                message=f"Generated {len(suggestions)} modification suggestions",
            )

        except Exception as e:
            return ModificationResponse(
                modification_id=modification_id,
                project_id=project_id,
                suggestions=[],
                status="failed",
                message=f"Failed to generate modifications: {str(e)}",
            )

    async def apply_modifications(
        self, apply_request: ApplyModificationRequest
    ) -> Dict[str, Any]:
        """Apply specific modifications to a project"""
        project_id = apply_request.project_id

        if project_id not in self.project_files:
            raise ValueError("Project not found")

        try:
            applied_modifications = await self._apply_modifications(
                project_id, apply_request.modification_ids, apply_request.custom_changes
            )

            # Update status
            if project_id in self.generation_status:
                status = self.generation_status[project_id]
                if not status.applied_modifications:
                    status.applied_modifications = []
                status.applied_modifications.extend(apply_request.modification_ids)

                # Remove applied suggestions from pending
                if status.pending_modifications:
                    status.pending_modifications = [
                        s
                        for s in status.pending_modifications
                        if s.suggestion_id not in apply_request.modification_ids
                    ]

            # Recreate ZIP file with modifications
            project_dir = self.output_dir / project_id
            await self._update_project_files(project_id, project_dir)
            zip_path = await self._create_zip_file(project_id, project_dir)

            return {
                "success": True,
                "applied_modifications": applied_modifications,
                "message": f"Applied {len(applied_modifications)} modifications successfully",
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to apply modifications: {str(e)}",
            }

    def _generate_enhanced_fallback_project_structure(
        self, analysis: ProjectAnalysis, error_message: str
    ) -> Dict[str, Any]:
        """Generate an enhanced fallback project structure based on analysis"""
        print(f"DEBUG: Generating enhanced fallback for {analysis.project_type}")

        # Determine if we need frontend, backend, or both
        tech_stack = analysis.technology_stack
        project_type = analysis.project_type

        files = {}

        # Always include README
        files[
            "README.md"
        ] = f"""# {analysis.suggested_name}

## Description
{analysis.reasoning[:500] if analysis.reasoning else 'AI-generated project with modern technologies.'}

## Project Type
{project_type.title()} application

## Technology Stack
- **Frontend**: {', '.join(tech_stack.frontend) if tech_stack.frontend else 'Not specified'}
- **Backend**: {', '.join(tech_stack.backend) if tech_stack.backend else 'Not specified'}
- **Database**: {', '.join(tech_stack.database) if tech_stack.database else 'Not specified'}
- **Styling**: {', '.join(tech_stack.styling) if tech_stack.styling else 'Not specified'}

## Key Features
{chr(10).join(f'- {feature}' for feature in analysis.key_features)}

## Setup Instructions
{self._generate_setup_instructions(analysis)}

## Architecture
Pattern: {analysis.architecture_pattern}
Complexity: {analysis.estimated_complexity}

Generated by AI Project Generator
"""

        # Generate frontend files if needed
        if project_type in ["fullstack", "frontend"] or tech_stack.frontend:
            files.update(self._generate_frontend_files(analysis))

        # Generate backend files if needed
        if project_type in ["fullstack", "backend", "api"] or tech_stack.backend:
            files.update(self._generate_backend_files(analysis))

        return {
            "files": files,
            "instructions": self._generate_setup_instructions(analysis),
            "next_steps": self._generate_next_steps(analysis),
            "additional_notes": f"Enhanced fallback project generated for {project_type} with {len(files)} files",
        }

    async def _generate_initial_suggestions(
        self,
        project_id: str,
        analysis: ProjectAnalysis,
        generated_files: Dict[str, str],
    ) -> List[ModificationSuggestion]:
        """Generate initial improvement suggestions"""
        try:
            # Create a prompt for initial suggestions
            suggestion_prompt = f"""
            Based on the generated project with {len(generated_files)} files, suggest 3-5 potential improvements or additional features that could enhance the project. Consider:
            1. Code quality improvements
            2. Additional features that would be valuable
            3. Performance optimizations
            4. Security enhancements
            5. Developer experience improvements

            Project type: {analysis.project_type}
            Technology stack: {analysis.technology_stack.model_dump_json()}
            """

            suggestions = await self.ai_service.suggest_modifications(
                project_id, suggestion_prompt, generated_files
            )

            return suggestions[:5]  # Limit to 5 initial suggestions

        except Exception:
            # Return empty list if suggestion generation fails
            return []

    async def _apply_modifications(
        self,
        project_id: str,
        modification_ids: List[str],
        custom_changes: Optional[Dict[str, str]] = None,
    ) -> List[str]:
        """Apply modifications to project files"""
        if project_id not in self.modification_suggestions:
            return []

        applied = []
        current_files = self.project_files[project_id]

        # Apply AI suggestions
        for suggestion in self.modification_suggestions[project_id]:
            if suggestion.suggestion_id in modification_ids:
                for file_mod in suggestion.files:
                    if file_mod.action == "create" or file_mod.action == "modify":
                        if file_mod.content:
                            current_files[file_mod.file_path] = file_mod.content
                    elif file_mod.action == "delete":
                        if file_mod.file_path in current_files:
                            del current_files[file_mod.file_path]

                applied.append(suggestion.suggestion_id)

        # Apply custom changes
        if custom_changes:
            for file_path, content in custom_changes.items():
                current_files[file_path] = content

        return applied

    async def _update_project_files(self, project_id: str, project_dir: Path):
        """Update project files on disk"""
        current_files = self.project_files[project_id]

        # Clear existing files
        if project_dir.exists():
            import shutil

            shutil.rmtree(project_dir)
        project_dir.mkdir(exist_ok=True)

        # Write updated files atomically
        import tempfile
        import shutil

        for file_path, content in current_files.items():
            full_path = project_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)

            # Write to temporary file first, then move to final location
            with tempfile.NamedTemporaryFile(
                mode="w",
                encoding="utf-8",
                delete=False,
                dir=full_path.parent,
                suffix=".tmp",
            ) as temp_file:
                temp_file.write(content)
                temp_file.flush()

            # Atomic move to final location
            shutil.move(temp_file.name, full_path)

    async def _create_zip_file(self, project_id: str, project_dir: Path) -> str:
        """Create a ZIP file of the generated project"""
        zip_path = self.output_dir / f"{project_id}.zip"

        with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for file_path in project_dir.rglob("*"):
                if file_path.is_file():
                    arcname = file_path.relative_to(project_dir)
                    zipf.write(file_path, arcname)

        return str(zip_path)

    def get_enhanced_generation_status(
        self, project_id: str
    ) -> Optional[EnhancedGenerationStatus]:
        """Get the enhanced generation status for a project"""
        return self.generation_status.get(project_id)

    def get_project_analysis(self, project_id: str) -> Optional[ProjectAnalysis]:
        """Get the AI analysis for a project"""
        return self.project_analyses.get(project_id)

    def get_modification_suggestions(
        self, project_id: str
    ) -> List[ModificationSuggestion]:
        """Get modification suggestions for a project"""
        return self.modification_suggestions.get(project_id, [])

    def get_download_path(self, project_id: str) -> Optional[str]:
        """Get the download path for a completed project"""
        zip_path = self.output_dir / f"{project_id}.zip"
        return str(zip_path) if zip_path.exists() else None

    def _generate_frontend_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate frontend files based on analysis"""
        files = {}
        tech_stack = analysis.technology_stack
        project_name = analysis.suggested_name

        # Determine frontend framework
        frontend_tech = (
            tech_stack.frontend[0].lower() if tech_stack.frontend else "react"
        )

        if "react" in frontend_tech or "next" in frontend_tech:
            files.update(self._generate_react_files(analysis))
        elif "vue" in frontend_tech:
            files.update(self._generate_vue_files(analysis))
        elif "angular" in frontend_tech:
            files.update(self._generate_angular_files(analysis))
        else:
            # Default to React
            files.update(self._generate_react_files(analysis))

        return files

    def _generate_backend_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate backend files based on analysis"""
        files = {}
        tech_stack = analysis.technology_stack

        # Determine backend framework
        backend_tech = (
            tech_stack.backend[0].lower() if tech_stack.backend else "fastapi"
        )

        if "fastapi" in backend_tech or "python" in backend_tech:
            files.update(self._generate_fastapi_files(analysis))
        elif "express" in backend_tech or "node" in backend_tech:
            files.update(self._generate_express_files(analysis))
        elif "django" in backend_tech:
            files.update(self._generate_django_files(analysis))
        else:
            # Default to FastAPI
            files.update(self._generate_fastapi_files(analysis))

        return files

    def _generate_setup_instructions(self, analysis: ProjectAnalysis) -> str:
        """Generate setup instructions based on technology stack"""
        tech_stack = analysis.technology_stack
        instructions = []

        if tech_stack.frontend and any(
            "react" in tech.lower() for tech in tech_stack.frontend
        ):
            instructions.extend(
                [
                    "## Frontend Setup",
                    "1. Navigate to frontend directory: `cd frontend`",
                    "2. Install dependencies: `npm install`",
                    "3. Start development server: `npm run dev`",
                    "4. Open http://localhost:3000",
                ]
            )

        if tech_stack.backend and any(
            "fastapi" in tech.lower() or "python" in tech.lower()
            for tech in tech_stack.backend
        ):
            instructions.extend(
                [
                    "## Backend Setup",
                    "1. Navigate to backend directory: `cd backend`",
                    "2. Create virtual environment: `python -m venv venv`",
                    "3. Activate virtual environment: `source venv/bin/activate` (Linux/Mac) or `venv\\Scripts\\activate` (Windows)",
                    "4. Install dependencies: `pip install -r requirements.txt`",
                    "5. Start server: `uvicorn main:app --reload`",
                    "6. API available at http://localhost:8000",
                ]
            )

        return (
            "\n".join(instructions)
            if instructions
            else "1. Follow standard setup for your chosen technology stack"
        )

    def _generate_next_steps(self, analysis: ProjectAnalysis) -> List[str]:
        """Generate next steps based on analysis"""
        steps = ["Install dependencies", "Configure environment variables"]

        if analysis.project_type == "fullstack":
            steps.extend(["Start backend server", "Start frontend development server"])
        elif analysis.project_type == "frontend":
            steps.append("Start development server")
        elif analysis.project_type == "backend":
            steps.append("Start API server")

        if analysis.technology_stack.database:
            steps.append("Set up database connection")

        steps.extend(["Review generated code", "Customize as needed"])
        return steps

    def _generate_react_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate React frontend files"""
        project_name = analysis.suggested_name
        features = analysis.key_features

        files = {
            "frontend/package.json": f"""{{"name": "{project_name}-frontend",
  "version": "1.0.0",
  "type": "module",
  "scripts": {{
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "lint": "eslint src --ext .js,.jsx,.ts,.tsx"
  }},
  "dependencies": {{
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "axios": "^1.6.0",
    "react-router-dom": "^6.8.0"
  }},
  "devDependencies": {{
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "@vitejs/plugin-react": "^4.0.0",
    "eslint": "^8.38.0",
    "typescript": "^5.0.2",
    "vite": "^4.3.0"
  }}
}}""",
            "frontend/vite.config.ts": """import {{ defineConfig }} from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({{
  plugins: [react()],
  server: {{
    port: 3000,
    open: true
  }},
  build: {{
    outDir: 'dist',
    sourcemap: true
  }}
}})""",
            "frontend/index.html": f"""<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{project_name}</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>""",
            "frontend/src/main.tsx": """import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)""",
            "frontend/src/App.tsx": f"""import React from 'react'
import {{ BrowserRouter as Router, Routes, Route }} from 'react-router-dom'
import Header from './components/Header'
import Home from './pages/Home'
import './App.css'

function App() {{
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={{<Home />}} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}}

export default App""",
            "frontend/src/components/Header.tsx": f"""import React from 'react'

const Header: React.FC = () => {{
  return (
    <header className="bg-blue-600 text-white shadow-lg">
      <div className="container mx-auto px-4 py-6">
        <h1 className="text-3xl font-bold">{project_name}</h1>
        <p className="text-blue-100 mt-2">Built with React and TypeScript</p>
      </div>
    </header>
  )
}}

export default Header""",
            "frontend/src/pages/Home.tsx": f"""import React from 'react'

const Home: React.FC = () => {{
  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-md p-8">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Welcome to {project_name}</h2>
        <p className="text-gray-600 mb-6">
          This is your AI-generated React application. Start building amazing features!
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-blue-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-blue-900 mb-2">Features</h3>
            <ul className="text-blue-700 space-y-1">
              {', '.join(f'<li>• {feature}</li>' for feature in features[:5])}
            </ul>
          </div>

          <div className="bg-green-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-green-900 mb-2">Next Steps</h3>
            <ul className="text-green-700 space-y-1">
              <li>• Customize the design</li>
              <li>• Add your business logic</li>
              <li>• Connect to backend APIs</li>
              <li>• Deploy to production</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}}

export default Home""",
            "frontend/src/index.css": """@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}""",
            "frontend/src/App.css": """.App {
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}""",
        }

        return files

    def _generate_fastapi_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate FastAPI backend files"""
        project_name = analysis.suggested_name
        features = analysis.key_features

        files = {
            "backend/requirements.txt": """fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
sqlalchemy==2.0.23
alembic==1.13.0
python-dotenv==1.0.0
python-multipart==0.0.6
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0""",
            "backend/main.py": f"""from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional
import uvicorn

app = FastAPI(
    title="{project_name} API",
    description="Backend API for {project_name}",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class Item(BaseModel):
    id: Optional[int] = None
    name: str
    description: Optional[str] = None

class ItemCreate(BaseModel):
    name: str
    description: Optional[str] = None

# In-memory storage (replace with database)
items_db = []
next_id = 1

@app.get("/")
async def root():
    return {{"message": "Welcome to {project_name} API", "version": "1.0.0"}}

@app.get("/health")
async def health_check():
    return {{"status": "healthy", "service": "{project_name} API"}}

@app.get("/items", response_model=List[Item])
async def get_items():
    return items_db

@app.post("/items", response_model=Item)
async def create_item(item: ItemCreate):
    global next_id
    new_item = Item(id=next_id, name=item.name, description=item.description)
    items_db.append(new_item)
    next_id += 1
    return new_item

@app.get("/items/{{item_id}}", response_model=Item)
async def get_item(item_id: int):
    for item in items_db:
        if item.id == item_id:
            return item
    raise HTTPException(status_code=404, detail="Item not found")

@app.put("/items/{{item_id}}", response_model=Item)
async def update_item(item_id: int, item_update: ItemCreate):
    for i, item in enumerate(items_db):
        if item.id == item_id:
            items_db[i] = Item(id=item_id, name=item_update.name, description=item_update.description)
            return items_db[i]
    raise HTTPException(status_code=404, detail="Item not found")

@app.delete("/items/{{item_id}}")
async def delete_item(item_id: int):
    for i, item in enumerate(items_db):
        if item.id == item_id:
            del items_db[i]
            return {{"message": "Item deleted successfully"}}
    raise HTTPException(status_code=404, detail="Item not found")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)""",
            "backend/.env.example": f"""# {project_name} Backend Environment Variables
DATABASE_URL=sqlite:///./app.db
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
ALLOWED_ORIGINS=http://localhost:3000

# API Settings
API_V1_STR=/api/v1
PROJECT_NAME={project_name}""",
            "backend/models.py": """from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime

Base = declarative_base()

class Item(Base):
    __tablename__ = "items"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    is_active = Column(Boolean, default=True)

class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(100), unique=True, index=True, nullable=False)
    username = Column(String(50), unique=True, index=True, nullable=False)
    hashed_password = Column(String(100), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())""",
            "backend/database.py": """from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv

load_dotenv()

DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./app.db")

engine = create_engine(
    DATABASE_URL,
    connect_args={{"check_same_thread": False}} if "sqlite" in DATABASE_URL else {{}}
)

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()""",
        }

        return files

    # Placeholder methods for other frameworks
    def _generate_vue_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate Vue.js frontend files"""
        # TODO: Implement Vue.js file generation
        return {{"frontend/README.md": "Vue.js project structure coming soon..."}}

    def _generate_angular_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate Angular frontend files"""
        # TODO: Implement Angular file generation
        return {{"frontend/README.md": "Angular project structure coming soon..."}}

    def _generate_express_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate Express.js backend files"""
        # TODO: Implement Express.js file generation
        return {{"backend/README.md": "Express.js project structure coming soon..."}}

    def _generate_django_files(self, analysis: ProjectAnalysis) -> Dict[str, str]:
        """Generate Django backend files"""
        # TODO: Implement Django file generation
        return {{"backend/README.md": "Django project structure coming soon..."}}
