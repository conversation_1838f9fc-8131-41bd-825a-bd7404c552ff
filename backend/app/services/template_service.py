import os
import json
from typing import Dict, Any
from pathlib import Path
from app.models.project_models import ProjectType


class TemplateService:
    def __init__(self):
        self.templates_dir = Path(__file__).parent.parent / "templates"

    def get_template_files(self, project_type: ProjectType) -> Dict[str, str]:
        """Get template files for a specific project type"""

        if project_type == ProjectType.REACT:
            return self._get_react_template()
        elif project_type == ProjectType.PYTHON:
            return self._get_python_template()
        elif project_type == ProjectType.REACT_NATIVE:
            return self._get_react_native_template()
        elif project_type == ProjectType.NEXTJS:
            return self._get_nextjs_template()
        elif project_type == ProjectType.FASTAPI:
            return self._get_fastapi_template()
        elif project_type == ProjectType.DJANGO:
            return self._get_django_template()
        elif project_type == ProjectType.FLASK:
            return self._get_flask_template()
        else:
            return {}

    def _get_react_template(self) -> Dict[str, str]:
        """Get React project template files"""
        return {
            "package.json": json.dumps(
                {
                    "name": "react-project",
                    "version": "0.1.0",
                    "private": True,
                    "dependencies": {
                        "react": "^18.2.0",
                        "react-dom": "^18.2.0",
                        "react-scripts": "5.0.1",
                    },
                    "scripts": {
                        "start": "react-scripts start",
                        "build": "react-scripts build",
                        "test": "react-scripts test",
                        "eject": "react-scripts eject",
                    },
                    "eslintConfig": {"extends": ["react-app", "react-app/jest"]},
                    "browserslist": {
                        "production": [">0.2%", "not dead", "not op_mini all"],
                        "development": [
                            "last 1 chrome version",
                            "last 1 firefox version",
                            "last 1 safari version",
                        ],
                    },
                },
                indent=2,
            ),
            "public/index.html": """<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Generated React App" />
    <title>React App</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>""",
            "src/index.js": """import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);""",
            "src/App.js": """import React from 'react';
import './App.css';

function App() {
  return (
    <div className="App">
      <header className="App-header">
        <h1>Welcome to Your Generated React App</h1>
        <p>This project was generated by AI Project Generator</p>
      </header>
    </div>
  );
}

export default App;""",
            "src/App.css": """.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
}""",
            "src/index.css": """body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}""",
            ".gitignore": """# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*""",
        }

    def _get_python_template(self) -> Dict[str, str]:
        """Get Python project template files"""
        return {
            "requirements.txt": """# Core dependencies
requests>=2.31.0
python-dotenv>=1.0.0

# Development dependencies
pytest>=7.4.0
black>=23.0.0
flake8>=6.0.0""",
            "main.py": '''#!/usr/bin/env python3
"""
Main application entry point
"""

def main():
    """Main function"""
    print("Hello from your generated Python project!")
    print("This project was created by AI Project Generator")

if __name__ == "__main__":
    main()''',
            "src/__init__.py": "# Python package",
            "tests/__init__.py": "# Tests package",
            "tests/test_main.py": '''import unittest
from main import main

class TestMain(unittest.TestCase):
    def test_main_function_exists(self):
        """Test that main function exists and is callable"""
        self.assertTrue(callable(main))

if __name__ == '__main__':
    unittest.main()''',
            ".gitignore": """# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~""",
            "README.md": """# Python Project

This is a generated Python project created by AI Project Generator.

## Setup

1. Create a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\\Scripts\\activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Run the application:
   ```bash
   python main.py
   ```

## Testing

Run tests with:
```bash
python -m pytest tests/
```""",
        }

    def _get_react_native_template(self) -> Dict[str, str]:
        """Get React Native project template files"""
        return {
            "package.json": json.dumps(
                {
                    "name": "ReactNativeProject",
                    "version": "0.0.1",
                    "private": True,
                    "scripts": {
                        "android": "react-native run-android",
                        "ios": "react-native run-ios",
                        "start": "react-native start",
                        "test": "jest",
                        "lint": "eslint .",
                    },
                    "dependencies": {"react": "18.2.0", "react-native": "0.72.6"},
                    "devDependencies": {
                        "@babel/core": "^7.20.0",
                        "@babel/preset-env": "^7.20.0",
                        "@babel/runtime": "^7.20.0",
                        "jest": "^29.2.1",
                        "metro-react-native-babel-preset": "0.76.8",
                    },
                },
                indent=2,
            )
        }

    def _get_nextjs_template(self) -> Dict[str, str]:
        """Get Next.js project template files"""
        return {}

    def _get_fastapi_template(self) -> Dict[str, str]:
        """Get FastAPI project template files"""
        return {}

    def _get_django_template(self) -> Dict[str, str]:
        """Get Django project template files"""
        return {}

    def _get_flask_template(self) -> Dict[str, str]:
        """Get Flask project template files"""
        return {}
