import os
import uuid
import asyncio
import zipfile
import tempfile
import shutil
from typing import Dict, Any, Optional
from pathlib import Path
from app.models.project_models import ProjectRequest, ProjectResponse, GenerationStatus, ProjectType
from app.services.ai_service import AIService
from app.services.template_service import TemplateService
from app.utils.file_utils import FileUtils


class ProjectGenerator:
    def __init__(self):
        self.ai_service = AIService()
        self.template_service = TemplateService()
        self.file_utils = FileUtils()
        self.generation_status: Dict[str, GenerationStatus] = {}
        self.output_dir = Path("generated_projects")
        self.output_dir.mkdir(exist_ok=True)
    
    async def generate_project(self, request: ProjectRequest) -> ProjectResponse:
        """Generate a complete project based on the request"""
        project_id = str(uuid.uuid4())
        
        # Initialize status tracking
        self.generation_status[project_id] = GenerationStatus(
            project_id=project_id,
            status="pending",
            progress=0,
            current_step="Initializing project generation"
        )
        
        try:
            # Start generation in background
            asyncio.create_task(self._generate_project_async(project_id, request))
            
            return ProjectResponse(
                project_id=project_id,
                status="pending",
                message="Project generation started successfully"
            )
        except Exception as e:
            self.generation_status[project_id].status = "failed"
            self.generation_status[project_id].error_message = str(e)
            
            return ProjectResponse(
                project_id=project_id,
                status="failed",
                message=f"Failed to start project generation: {str(e)}"
            )
    
    async def _generate_project_async(self, project_id: str, request: ProjectRequest):
        """Async project generation process"""
        try:
            status = self.generation_status[project_id]
            
            # Step 1: Generate project structure with AI
            status.current_step = "Generating project structure with AI"
            status.progress = 20
            
            ai_result = await self.ai_service.generate_project_structure(
                provider=request.ai_provider,
                project_type=request.project_type,
                project_name=request.project_name,
                description=request.description,
                features=request.features,
                additional_requirements=request.additional_requirements
            )
            
            # Step 2: Enhance with templates
            status.current_step = "Enhancing with project templates"
            status.progress = 40
            
            template_files = self.template_service.get_template_files(request.project_type)
            enhanced_files = self._merge_ai_and_template_files(ai_result["files"], template_files)
            
            # Step 3: Create project directory
            status.current_step = "Creating project files"
            status.progress = 60
            
            project_dir = self.output_dir / project_id
            project_dir.mkdir(exist_ok=True)
            
            # Step 4: Write files
            status.current_step = "Writing project files"
            status.progress = 80
            
            generated_files = []
            for file_path, content in enhanced_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)
                
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                generated_files.append(file_path)
            
            # Step 5: Create ZIP file
            status.current_step = "Creating downloadable package"
            status.progress = 90
            
            zip_path = await self._create_zip_file(project_id, project_dir)
            
            # Step 6: Complete
            status.status = "completed"
            status.progress = 100
            status.current_step = "Project generation completed"
            
            # Update with download URL
            download_url = f"/api/download/{project_id}"
            
        except Exception as e:
            status.status = "failed"
            status.error_message = str(e)
            status.current_step = f"Generation failed: {str(e)}"
    
    def _merge_ai_and_template_files(self, ai_files: Dict[str, str], template_files: Dict[str, str]) -> Dict[str, str]:
        """Merge AI-generated files with template files"""
        merged = template_files.copy()
        
        # AI files take precedence, but we keep template files that AI didn't generate
        for file_path, content in ai_files.items():
            merged[file_path] = content
        
        return merged
    
    async def _create_zip_file(self, project_id: str, project_dir: Path) -> str:
        """Create a ZIP file of the generated project"""
        zip_path = self.output_dir / f"{project_id}.zip"
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for file_path in project_dir.rglob('*'):
                if file_path.is_file():
                    arcname = file_path.relative_to(project_dir)
                    zipf.write(file_path, arcname)
        
        return str(zip_path)
    
    def get_generation_status(self, project_id: str) -> Optional[GenerationStatus]:
        """Get the current generation status for a project"""
        return self.generation_status.get(project_id)
    
    def get_download_path(self, project_id: str) -> Optional[str]:
        """Get the download path for a completed project"""
        zip_path = self.output_dir / f"{project_id}.zip"
        return str(zip_path) if zip_path.exists() else None
    
    def cleanup_old_projects(self, max_age_hours: int = 24):
        """Clean up old generated projects"""
        import time
        current_time = time.time()
        
        for item in self.output_dir.iterdir():
            if item.stat().st_mtime < current_time - (max_age_hours * 3600):
                if item.is_file():
                    item.unlink()
                elif item.is_dir():
                    shutil.rmtree(item)
