import os
from typing import Dict, Any, Optional
from langchain.llms import OpenAI
from langchain.chat_models import ChatOpenAI
from langchain.schema import HumanMessage, SystemMessage
from langchain.prompts import PromptTemplate
import openai
import requests
from app.models.project_models import AI<PERSON>rovider, ProjectType


class AIService:
    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.huggingface_api_key = os.getenv("HUGGINGFACE_API_KEY")
        self.grok_api_key = os.getenv("GROK_API_KEY")

    async def generate_project_structure(
        self,
        provider: AIProvider,
        project_type: ProjectType,
        project_name: str,
        description: str,
        features: list,
        additional_requirements: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Generate project structure using specified AI provider"""

        prompt = self._create_project_prompt(
            project_type, project_name, description, features, additional_requirements
        )

        if provider == AIProvider.OPENAI:
            return await self._generate_with_openai(prompt)
        elif provider == AIProvider.HUGGINGFACE:
            return await self._generate_with_huggingface(prompt)
        elif provider == AIProvider.GROK:
            return await self._generate_with_grok(prompt)
        else:
            raise ValueError(f"Unsupported AI provider: {provider}")

    def _create_project_prompt(
        self,
        project_type: ProjectType,
        project_name: str,
        description: str,
        features: list,
        additional_requirements: Optional[str] = None,
    ) -> str:
        """Create a detailed prompt for project generation"""

        base_prompt = f"""
You are an expert software architect and developer. Generate a complete project structure for a {project_type.value} project.

Project Details:
- Name: {project_name}
- Type: {project_type.value}
- Description: {description}
- Features: {', '.join(features) if features else 'Standard features'}
- Additional Requirements: {additional_requirements or 'None'}

Please provide:
1. Complete folder structure
2. All necessary configuration files
3. Main application files with basic implementation
4. Package.json/requirements.txt with appropriate dependencies
5. README.md with setup instructions
6. Basic styling and components (for frontend projects)
7. API endpoints and models (for backend projects)

Return the response as a JSON object with the following structure:
{{
    "files": {{
        "path/to/file.ext": "file content here",
        "another/file.ext": "content here"
    }},
    "instructions": "Setup and run instructions"
}}

Make sure the generated code is production-ready, follows best practices, and includes proper error handling.
"""
        return base_prompt

    async def _generate_with_openai(self, prompt: str) -> Dict[str, Any]:
        """Generate project using OpenAI GPT"""
        try:
            chat = ChatOpenAI(
                openai_api_key=self.openai_api_key, model_name="gpt-4", temperature=0.3
            )

            messages = [
                SystemMessage(content="You are an expert software architect."),
                HumanMessage(content=prompt),
            ]

            response = await chat.agenerate([messages])
            return self._parse_ai_response(response.generations[0][0].text)
        except Exception as e:
            raise Exception(f"OpenAI generation failed: {str(e)}")

    async def _generate_with_huggingface(self, prompt: str) -> Dict[str, Any]:
        """Generate project using Hugging Face"""
        try:
            # Using Hugging Face Inference API with code-generation models
            headers = {"Authorization": f"Bearer {self.huggingface_api_key}"}

            # Try multiple models for better code generation
            models_to_try = [
                "microsoft/CodeGPT-small-py",  # Code generation model
                "microsoft/DialoGPT-large",  # Conversational model
                "bigcode/starcoder",  # Code-specific model
            ]

            for model in models_to_try:
                try:
                    api_url = f"https://api-inference.huggingface.co/models/{model}"
                    payload = {
                        "inputs": prompt,
                        "parameters": {
                            "max_length": 2000,
                            "temperature": 0.3,
                            "do_sample": True,
                        },
                    }

                    response = requests.post(
                        api_url, headers=headers, json=payload, timeout=30
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if isinstance(result, list) and len(result) > 0:
                            generated_text = result[0].get("generated_text", "")
                            if generated_text and len(generated_text) > len(prompt):
                                return self._parse_ai_response(generated_text)
                        elif isinstance(result, dict) and "generated_text" in result:
                            return self._parse_ai_response(result["generated_text"])
                except Exception:
                    continue  # Try next model

            # If all Hugging Face models fail, fallback to OpenAI
            return await self._generate_with_openai(prompt)

        except Exception as e:
            # Fallback to OpenAI if Hugging Face fails completely
            return await self._generate_with_openai(prompt)

    async def _generate_with_grok(self, prompt: str) -> Dict[str, Any]:
        """Generate project using Grok"""
        try:
            # Using Grok API (X.AI)
            headers = {
                "Authorization": f"Bearer {self.grok_api_key}",
                "Content-Type": "application/json",
            }

            # Grok API endpoint
            api_url = "https://api.x.ai/v1/chat/completions"

            payload = {
                "messages": [
                    {
                        "role": "system",
                        "content": "You are an expert software architect.",
                    },
                    {"role": "user", "content": prompt},
                ],
                "model": "grok-beta",
                "temperature": 0.3,
                "max_tokens": 4000,
            }

            response = requests.post(api_url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if "choices" in result and len(result["choices"]) > 0:
                    content = result["choices"][0]["message"]["content"]
                    return self._parse_ai_response(content)
                else:
                    # Fallback to OpenAI if Grok fails
                    return await self._generate_with_openai(prompt)
            else:
                # Fallback to OpenAI if Grok fails
                return await self._generate_with_openai(prompt)

        except Exception as e:
            # Fallback to OpenAI if Grok fails
            return await self._generate_with_openai(prompt)

    def _parse_ai_response(self, response_text: str) -> Dict[str, Any]:
        """Parse AI response and extract project structure"""
        try:
            import json

            # Try to extract JSON from the response
            start_idx = response_text.find("{")
            end_idx = response_text.rfind("}") + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response_text[start_idx:end_idx]
                return json.loads(json_str)
            else:
                # Fallback: create a basic structure
                return {
                    "files": {"README.md": "# Generated Project\n\n" + response_text},
                    "instructions": "Please review the generated content and set up manually.",
                }
        except Exception as e:
            return {
                "files": {
                    "README.md": f"# Generated Project\n\nGeneration completed but parsing failed.\n\nRaw response:\n{response_text}"
                },
                "instructions": "Please review the generated content.",
            }
