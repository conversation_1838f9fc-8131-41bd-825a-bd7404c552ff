import os
import asyncio
from pathlib import Path
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from dotenv import load_dotenv

from app.models.project_models import (
    ProjectRequest,
    ProjectResponse,
    GenerationStatus,
    IntelligentProjectRequest,
    IntelligentProjectResponse,
    ModificationRequest,
    ApplyModificationRequest,
)
from app.services.project_generator import ProjectGenerator
from app.services.intelligent_project_generator import IntelligentProjectGenerator

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="AI Project Generator",
    description="Generate project skeletons using AI",
    version="1.0.0",
)

# Configure CORS
allowed_origins = os.getenv(
    "ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:5173"
).split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
project_generator = ProjectGenerator()
intelligent_project_generator = IntelligentProjectGenerator()

# Create generated_projects directory
generated_projects_dir = Path("generated_projects")
generated_projects_dir.mkdir(exist_ok=True)


@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "AI Project Generator API", "version": "1.0.0", "docs": "/docs"}


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "AI Project Generator"}


@app.post("/api/generate", response_model=ProjectResponse)
async def generate_project(request: ProjectRequest, background_tasks: BackgroundTasks):
    """Generate a new project based on the request"""
    try:
        # Validate request
        if not request.project_name.strip():
            raise HTTPException(status_code=400, detail="Project name is required")

        if not request.description.strip():
            raise HTTPException(
                status_code=400, detail="Project description is required"
            )

        # Start project generation
        response = await project_generator.generate_project(request)

        # Add cleanup task
        background_tasks.add_task(
            project_generator.cleanup_old_projects, max_age_hours=24
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to generate project: {str(e)}"
        )


@app.post("/api/generate/intelligent", response_model=IntelligentProjectResponse)
async def generate_intelligent_project(
    request: IntelligentProjectRequest, background_tasks: BackgroundTasks
):
    """Generate a new project using intelligent AI analysis"""
    try:
        # Validate request
        if not request.prompt.strip():
            raise HTTPException(status_code=400, detail="Project prompt is required")

        # Start intelligent project generation
        response = await intelligent_project_generator.generate_intelligent_project(
            request
        )

        # Add cleanup task
        background_tasks.add_task(
            project_generator.cleanup_old_projects, max_age_hours=24
        )

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to generate intelligent project: {str(e)}"
        )


@app.get("/api/status/{project_id}", response_model=GenerationStatus)
async def get_generation_status(project_id: str):
    """Get the generation status for a project"""
    status = project_generator.get_generation_status(project_id)

    if not status:
        raise HTTPException(status_code=404, detail="Project not found")

    return status


@app.get("/api/download/{project_id}")
async def download_project(project_id: str):
    """Download the generated project as a ZIP file"""
    zip_path = project_generator.get_download_path(project_id)

    if not zip_path or not Path(zip_path).exists():
        raise HTTPException(status_code=404, detail="Project file not found")

    # Get project status to determine project name
    status = project_generator.get_generation_status(project_id)
    filename = f"generated_project_{project_id}.zip"

    return FileResponse(path=zip_path, filename=filename, media_type="application/zip")


@app.get("/api/projects")
async def list_projects():
    """List all generated projects (both classic and intelligent)"""
    projects = []

    # Add classic projects
    for project_id, status in project_generator.generation_status.items():
        project_info = {
            "project_id": project_id,
            "project_type": "classic",
            "status": status.status,
            "progress": status.progress,
            "current_step": status.current_step,
            "created_at": getattr(status, "created_at", None),
        }

        if status.status == "completed":
            zip_path = project_generator.get_download_path(project_id)
            if zip_path and Path(zip_path).exists():
                project_info["download_available"] = True
                project_info["file_size"] = Path(zip_path).stat().st_size
            else:
                project_info["download_available"] = False

        projects.append(project_info)

    # Add intelligent projects
    for project_id, status in intelligent_project_generator.generation_status.items():
        project_info = {
            "project_id": project_id,
            "project_type": "intelligent",
            "status": status.status,
            "progress": status.progress,
            "current_step": status.current_step,
            "phase": getattr(status, "phase", "unknown"),
            "created_at": getattr(status, "created_at", None),
        }

        # Add analysis info if available
        if project_id in intelligent_project_generator.project_analyses:
            analysis = intelligent_project_generator.project_analyses[project_id]
            project_info.update(
                {
                    "project_name": analysis.suggested_name,
                    "suggested_name": analysis.suggested_name,
                    "project_description": f"{analysis.project_type.title()} application",
                    "analysis": {
                        "suggested_name": analysis.suggested_name,
                        "project_type": analysis.project_type,
                        "complexity": analysis.estimated_complexity,
                        "key_features": (
                            analysis.key_features[:3] if analysis.key_features else []
                        ),  # First 3 features
                        "technology_stack": {
                            "frontend": (
                                analysis.technology_stack.frontend
                                if analysis.technology_stack
                                else []
                            ),
                            "backend": (
                                analysis.technology_stack.backend
                                if analysis.technology_stack
                                else []
                            ),
                            "database": (
                                analysis.technology_stack.database
                                if analysis.technology_stack
                                else []
                            ),
                        },
                    },
                }
            )
        else:
            # Fallback info when no analysis available
            project_info.update(
                {
                    "project_name": f"Project {project_id[:8]}",
                    "suggested_name": f"ai-project-{project_id[:8]}",
                    "project_description": "AI-generated project",
                }
            )

        # Check download availability
        if status.status == "completed":
            zip_path = intelligent_project_generator.get_download_path(project_id)
            if zip_path and Path(zip_path).exists():
                project_info["download_available"] = True
                project_info["file_size"] = Path(zip_path).stat().st_size

                # Add file count if available
                if hasattr(status, "files") and status.files:
                    project_info["file_count"] = len(status.files)
            else:
                project_info["download_available"] = False

        # Add error info if failed
        if status.status == "failed" and hasattr(status, "error_message"):
            project_info["error_message"] = status.error_message

        projects.append(project_info)

    # Sort by creation time (newest first) if available, otherwise by project_id
    projects.sort(key=lambda x: x.get("created_at") or x["project_id"], reverse=True)

    return {
        "projects": projects,
        "total_count": len(projects),
        "classic_count": len([p for p in projects if p["project_type"] == "classic"]),
        "intelligent_count": len(
            [p for p in projects if p["project_type"] == "intelligent"]
        ),
    }


@app.delete("/api/projects/{project_id}")
async def delete_project(project_id: str):
    """Delete a generated project (both classic and intelligent)"""
    try:
        deleted_from = []

        # Remove from classic project generator
        if project_id in project_generator.generation_status:
            del project_generator.generation_status[project_id]
            deleted_from.append("classic")

        # Remove from intelligent project generator
        if project_id in intelligent_project_generator.generation_status:
            del intelligent_project_generator.generation_status[project_id]
            deleted_from.append("intelligent")

        # Remove from intelligent project analyses
        if project_id in intelligent_project_generator.project_analyses:
            del intelligent_project_generator.project_analyses[project_id]

        # Remove from intelligent project files
        if (
            hasattr(intelligent_project_generator, "project_files")
            and project_id in intelligent_project_generator.project_files
        ):
            del intelligent_project_generator.project_files[project_id]

        # Remove from modification suggestions
        if (
            hasattr(intelligent_project_generator, "modification_suggestions")
            and project_id in intelligent_project_generator.modification_suggestions
        ):
            del intelligent_project_generator.modification_suggestions[project_id]

        # Delete project files from both possible locations
        classic_project_dir = generated_projects_dir / project_id
        classic_zip_path = generated_projects_dir / f"{project_id}.zip"

        intelligent_project_dir = intelligent_project_generator.output_dir / project_id
        intelligent_zip_path = (
            intelligent_project_generator.output_dir / f"{project_id}.zip"
        )

        # Delete classic project files
        if classic_project_dir.exists():
            import shutil

            shutil.rmtree(classic_project_dir)

        if classic_zip_path.exists():
            classic_zip_path.unlink()

        # Delete intelligent project files
        if intelligent_project_dir.exists():
            import shutil

            shutil.rmtree(intelligent_project_dir)

        if intelligent_zip_path.exists():
            intelligent_zip_path.unlink()

        if not deleted_from:
            raise HTTPException(status_code=404, detail="Project not found")

        return {
            "message": "Project deleted successfully",
            "deleted_from": deleted_from,
            "project_id": project_id,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to delete project: {str(e)}"
        )


@app.get("/api/status/enhanced/{project_id}")
async def get_enhanced_generation_status(project_id: str):
    """Get the enhanced generation status for an intelligent project"""
    try:
        status = intelligent_project_generator.get_enhanced_generation_status(
            project_id
        )

        if not status:
            raise HTTPException(status_code=404, detail="Project not found")

        # Create a safe serializable version of the status
        status_dict = {
            "project_id": status.project_id,
            "status": status.status,
            "progress": status.progress,
            "current_step": status.current_step,
            "phase": status.phase,
            "estimated_time_remaining": status.estimated_time_remaining,
            "error_message": status.error_message,
            "can_modify": status.can_modify,
        }

        # Add analysis if available (safely)
        if status.analysis:
            try:
                if hasattr(status.analysis, "model_dump"):
                    status_dict["analysis"] = status.analysis.model_dump()
                else:
                    status_dict["analysis"] = {
                        "suggested_name": status.analysis.suggested_name,
                        "project_type": status.analysis.project_type,
                        "estimated_complexity": status.analysis.estimated_complexity,
                        "key_features": (
                            status.analysis.key_features[:3]
                            if status.analysis.key_features
                            else []
                        ),
                    }
            except Exception as analysis_error:
                print(f"DEBUG: Analysis serialization error: {analysis_error}")
                status_dict["analysis"] = None

        # Add pending modifications count (safely)
        if status.pending_modifications:
            try:
                status_dict["pending_modifications_count"] = len(
                    status.pending_modifications
                )
            except Exception:
                status_dict["pending_modifications_count"] = 0

        # Add applied modifications count (safely)
        if status.applied_modifications:
            try:
                status_dict["applied_modifications_count"] = len(
                    status.applied_modifications
                )
            except Exception:
                status_dict["applied_modifications_count"] = 0

        return status_dict

    except HTTPException:
        raise
    except Exception as e:
        print(f"DEBUG: Enhanced status error: {e}")
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Error retrieving status: {str(e)}"
        )


@app.get("/api/debug/projects")
async def debug_projects():
    """Debug endpoint to see what projects exist"""
    try:
        # Check both generators
        classic_projects = (
            list(project_generator.generation_status.keys())
            if hasattr(project_generator, "generation_status")
            else []
        )
        intelligent_projects = (
            list(intelligent_project_generator.generation_status.keys())
            if hasattr(intelligent_project_generator, "generation_status")
            else []
        )
        intelligent_analyses = (
            list(intelligent_project_generator.project_analyses.keys())
            if hasattr(intelligent_project_generator, "project_analyses")
            else []
        )

        # Get detailed status for intelligent projects
        intelligent_details = {}
        for project_id in intelligent_projects:
            try:
                status = intelligent_project_generator.generation_status.get(project_id)
                if status:
                    intelligent_details[project_id] = {
                        "status": status.status,
                        "progress": status.progress,
                        "phase": status.phase,
                        "current_step": status.current_step,
                        "has_analysis": project_id
                        in intelligent_project_generator.project_analyses,
                        "error_message": getattr(status, "error_message", None),
                    }
            except Exception as detail_error:
                intelligent_details[project_id] = {"error": str(detail_error)}

        return {
            "classic_projects": classic_projects,
            "intelligent_projects": intelligent_projects,
            "intelligent_analyses": intelligent_analyses,
            "intelligent_details": intelligent_details,
        }
    except Exception as e:
        return {"error": str(e)}


@app.post("/api/debug/test-analysis")
async def test_analysis(request: IntelligentProjectRequest):
    """Debug endpoint to test AI analysis directly"""
    try:
        print(f"DEBUG: Testing analysis with {request.ai_provider}")
        analysis = (
            await intelligent_project_generator.ai_service.analyze_project_requirements(
                request
            )
        )
        print(f"DEBUG: Analysis successful: {analysis.suggested_name}")

        return {
            "success": True,
            "analysis": (
                analysis.model_dump()
                if hasattr(analysis, "model_dump")
                else analysis.dict()
            ),
        }
    except Exception as e:
        print(f"DEBUG: Analysis failed: {e}")
        import traceback

        traceback.print_exc()
        return {"success": False, "error": str(e)}


@app.get("/api/analysis/{project_id}")
async def get_project_analysis(project_id: str):
    """Get the AI analysis for a project"""
    try:
        print(f"DEBUG: Requesting analysis for project {project_id}")

        # Check if project exists in intelligent generator
        if not hasattr(intelligent_project_generator, "project_analyses"):
            print("DEBUG: No project_analyses attribute found")
            raise HTTPException(status_code=404, detail="No analyses available")

        print(
            f"DEBUG: Available analyses: {list(intelligent_project_generator.project_analyses.keys())}"
        )

        analysis = intelligent_project_generator.get_project_analysis(project_id)
        print(f"DEBUG: Retrieved analysis: {analysis}")

        if not analysis:
            # Check if project exists in generation status but analysis is missing
            if project_id in intelligent_project_generator.generation_status:
                status = intelligent_project_generator.generation_status[project_id]
                print(
                    f"DEBUG: Project exists with status: {status.status}, phase: {status.phase}"
                )

                if status.status == "failed":
                    raise HTTPException(
                        status_code=500,
                        detail=f"Project generation failed: {status.error_message}",
                    )
                elif status.status in ["generating", "pending"]:
                    raise HTTPException(
                        status_code=202,
                        detail="Project is still being generated. Analysis not yet available.",
                    )
                else:
                    # Project completed but analysis is missing - this shouldn't happen
                    raise HTTPException(
                        status_code=500,
                        detail="Project completed but analysis is missing. This may indicate a generation error.",
                    )
            else:
                raise HTTPException(status_code=404, detail="Project not found")

        # Ensure we return a proper dict that FastAPI can serialize
        if hasattr(analysis, "model_dump"):
            result = analysis.model_dump()
            print(f"DEBUG: Returning model_dump result")
            return result
        elif hasattr(analysis, "dict"):
            result = analysis.dict()
            print(f"DEBUG: Returning dict result")
            return result
        else:
            print(f"DEBUG: Returning raw analysis")
            return analysis
    except HTTPException:
        raise
    except Exception as e:
        print(f"DEBUG: Exception in get_project_analysis: {e}")
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Error retrieving analysis: {str(e)}"
        )


@app.post("/api/modify/{project_id}")
async def request_project_modifications(project_id: str, request: ModificationRequest):
    """Request modifications to an existing project"""
    try:
        request.project_id = project_id
        response = await intelligent_project_generator.request_modifications(request)

        # Ensure we return a proper dict that FastAPI can serialize
        if hasattr(response, "model_dump"):
            return response.model_dump()
        elif hasattr(response, "dict"):
            return response.dict()
        else:
            return response

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to request modifications: {str(e)}"
        )


@app.post("/api/apply-modifications/{project_id}")
async def apply_project_modifications(
    project_id: str, request: ApplyModificationRequest
):
    """Apply specific modifications to a project"""
    try:
        request.project_id = project_id
        response = await intelligent_project_generator.apply_modifications(request)
        return response

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to apply modifications: {str(e)}"
        )


@app.get("/api/suggestions/{project_id}")
async def get_modification_suggestions(project_id: str):
    """Get modification suggestions for a project"""
    try:
        suggestions = intelligent_project_generator.get_modification_suggestions(
            project_id
        )

        # Convert suggestions to dicts if they are Pydantic models
        suggestions_data = []
        for suggestion in suggestions:
            if hasattr(suggestion, "model_dump"):
                suggestions_data.append(suggestion.model_dump())
            elif hasattr(suggestion, "dict"):
                suggestions_data.append(suggestion.dict())
            else:
                suggestions_data.append(suggestion)

        return {"suggestions": suggestions_data}

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get suggestions: {str(e)}"
        )


@app.post("/api/debug/test-generation")
async def test_generation(request: IntelligentProjectRequest):
    """Debug endpoint to test AI generation directly"""
    try:
        print(f"DEBUG: Testing full generation with {request.ai_provider}")

        # First analyze
        analysis = (
            await intelligent_project_generator.ai_service.analyze_project_requirements(
                request
            )
        )
        print(f"DEBUG: Analysis completed: {analysis.suggested_name}")

        # Then generate
        generation_result = (
            await intelligent_project_generator.ai_service.generate_intelligent_project(
                analysis, request
            )
        )
        print(
            f"DEBUG: Generation completed with {len(generation_result.get('files', {}))} files"
        )

        return {
            "success": True,
            "analysis": (
                analysis.model_dump()
                if hasattr(analysis, "model_dump")
                else analysis.dict()
            ),
            "generation": {
                "file_count": len(generation_result.get("files", {})),
                "files": list(generation_result.get("files", {}).keys()),
                "instructions": generation_result.get("instructions", ""),
                "next_steps": generation_result.get("next_steps", []),
            },
        }
    except Exception as e:
        print(f"DEBUG: Generation failed: {e}")
        import traceback

        traceback.print_exc()
        return {"success": False, "error": str(e)}


@app.post("/api/debug/quick-test")
async def quick_test_generation():
    """Quick test endpoint to verify intelligent generation works"""
    try:
        from app.models.project_models import IntelligentProjectRequest

        # Create a simple test request
        test_request = IntelligentProjectRequest(
            prompt="Create a simple todo app with React and TypeScript",
            ai_provider="openai",
            project_name="test-todo-app",
            complexity_level="simple",
            include_tests=True,
            include_documentation=True,
        )

        print("DEBUG: Starting quick test generation")
        response = await intelligent_project_generator.generate_intelligent_project(
            test_request
        )

        return {
            "success": True,
            "project_id": response.project_id,
            "status": response.status,
            "file_count": len(response.files),
            "files": response.files[:10],  # Show first 10 files
            "message": "Quick test completed successfully",
        }

    except Exception as e:
        print(f"DEBUG: Quick test failed: {e}")
        import traceback

        traceback.print_exc()
        return {"success": False, "error": str(e), "message": "Quick test failed"}


@app.post("/api/debug/create-sample-project")
async def create_sample_project():
    """Create a sample project for testing the project list"""
    try:
        from app.models.project_models import IntelligentProjectRequest

        # Create a sample project
        sample_request = IntelligentProjectRequest(
            prompt="Create a sample React dashboard with charts and user management",
            ai_provider="openai",
            project_name="sample-dashboard",
            complexity_level="medium",
            include_tests=True,
            include_documentation=True,
        )

        print("DEBUG: Creating sample project for testing")
        response = await intelligent_project_generator.generate_intelligent_project(
            sample_request
        )

        return {
            "success": True,
            "project_id": response.project_id,
            "message": "Sample project created successfully",
            "note": "You can now check /api/projects to see this project in the list",
        }

    except Exception as e:
        print(f"DEBUG: Sample project creation failed: {e}")
        import traceback

        traceback.print_exc()
        return {
            "success": False,
            "error": str(e),
            "message": "Sample project creation failed",
        }


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    from fastapi.responses import JSONResponse

    return JSONResponse(
        status_code=404, content={"error": "Not found", "detail": str(exc)}
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    from fastapi.responses import JSONResponse

    return JSONResponse(
        status_code=500, content={"error": "Internal server error", "detail": str(exc)}
    )


if __name__ == "__main__":
    import uvicorn

    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "True").lower() == "true"

    # Disable reload to prevent any file changes from restarting the server
    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=False,  # Completely disable reload
        log_level="info",
    )
