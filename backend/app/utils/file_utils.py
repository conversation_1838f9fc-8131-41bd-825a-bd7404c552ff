import os
import shutil
import zipfile
from pathlib import Path
from typing import Dict, List


class FileUtils:
    @staticmethod
    def create_directory(path: str) -> bool:
        """Create directory if it doesn't exist"""
        try:
            Path(path).mkdir(parents=True, exist_ok=True)
            return True
        except Exception as e:
            print(f"Error creating directory {path}: {e}")
            return False
    
    @staticmethod
    def write_file(file_path: str, content: str) -> bool:
        """Write content to file"""
        try:
            path = Path(file_path)
            path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"Error writing file {file_path}: {e}")
            return False
    
    @staticmethod
    def read_file(file_path: str) -> str:
        """Read content from file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return ""
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """Delete file"""
        try:
            Path(file_path).unlink()
            return True
        except Exception as e:
            print(f"Error deleting file {file_path}: {e}")
            return False
    
    @staticmethod
    def delete_directory(dir_path: str) -> bool:
        """Delete directory and all its contents"""
        try:
            shutil.rmtree(dir_path)
            return True
        except Exception as e:
            print(f"Error deleting directory {dir_path}: {e}")
            return False
    
    @staticmethod
    def create_zip(source_dir: str, zip_path: str) -> bool:
        """Create ZIP file from directory"""
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                source_path = Path(source_dir)
                for file_path in source_path.rglob('*'):
                    if file_path.is_file():
                        arcname = file_path.relative_to(source_path)
                        zipf.write(file_path, arcname)
            return True
        except Exception as e:
            print(f"Error creating ZIP file {zip_path}: {e}")
            return False
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """Get file size in bytes"""
        try:
            return Path(file_path).stat().st_size
        except Exception:
            return 0
    
    @staticmethod
    def list_files(directory: str, extension: str = None) -> List[str]:
        """List files in directory, optionally filtered by extension"""
        try:
            path = Path(directory)
            if extension:
                pattern = f"*.{extension.lstrip('.')}"
                return [str(f) for f in path.glob(pattern) if f.is_file()]
            else:
                return [str(f) for f in path.iterdir() if f.is_file()]
        except Exception as e:
            print(f"Error listing files in {directory}: {e}")
            return []
    
    @staticmethod
    def copy_file(source: str, destination: str) -> bool:
        """Copy file from source to destination"""
        try:
            shutil.copy2(source, destination)
            return True
        except Exception as e:
            print(f"Error copying file from {source} to {destination}: {e}")
            return False
    
    @staticmethod
    def move_file(source: str, destination: str) -> bool:
        """Move file from source to destination"""
        try:
            shutil.move(source, destination)
            return True
        except Exception as e:
            print(f"Error moving file from {source} to {destination}: {e}")
            return False
