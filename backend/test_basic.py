#!/usr/bin/env python3
"""
Basic test script to verify the backend setup
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all required modules can be imported"""
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
        
        import uvicorn
        print("✅ Uvicorn imported successfully")
        
        import pydantic
        print("✅ Pydantic imported successfully")
        
        from app.main import app
        print("✅ Main app imported successfully")
        
        from app.models.project_models import ProjectRequest, ProjectResponse
        print("✅ Project models imported successfully")
        
        from app.services.ai_service import AIService
        print("✅ AI service imported successfully")
        
        from app.services.project_generator import ProjectGenerator
        print("✅ Project generator imported successfully")
        
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_app_creation():
    """Test that the FastAPI app can be created"""
    try:
        from app.main import app
        assert app is not None
        print("✅ FastAPI app created successfully")
        return True
    except Exception as e:
        print(f"❌ App creation error: {e}")
        return False

def main():
    """Run basic tests"""
    print("🧪 Running basic backend tests...\n")
    
    tests = [
        ("Import Tests", test_imports),
        ("App Creation", test_app_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"Running {test_name}...")
        if test_func():
            passed += 1
        print()
    
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Backend setup is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check your setup.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
