#!/usr/bin/env python3
"""
Test script to verify LangGraph integration works correctly
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_langgraph_generator():
    """Test the LangGraph project generator"""
    try:
        from app.models.project_models import IntelligentProjectRequest
        from app.services.langgraph_project_generator import LangGraphProjectGenerator
        
        print("✅ Successfully imported LangGraph generator")
        
        # Create a test request
        test_request = IntelligentProjectRequest(
            prompt="Create a simple todo app with React frontend and FastAPI backend",
            project_name="test-todo-app",
            complexity_level="beginner",
            preferred_technologies=["React", "FastAPI"],
            target_platform="web",
            include_tests=True,
            include_documentation=True,
            include_ci_cd=False
        )
        
        print("✅ Created test request")
        
        # Initialize the generator
        generator = LangGraphProjectGenerator()
        print("✅ Initialized LangGraph generator")
        
        # Test project generation (this will use OpenAI API if available)
        if os.getenv("OPENAI_API_KEY"):
            print("🔑 OpenAI API key found, testing full generation...")
            try:
                result = await generator.generate_project(test_request)
                print(f"✅ Generated project with {len(result.get('files', {}))} files")
                
                # Print some sample files
                files = result.get('files', {})
                if files:
                    print("\n📁 Sample generated files:")
                    for file_path in list(files.keys())[:5]:  # Show first 5 files
                        print(f"  - {file_path}")
                
                print(f"✅ Setup instructions: {len(result.get('instructions', []))} steps")
                print(f"✅ Next steps: {len(result.get('next_steps', []))} items")
                
            except Exception as e:
                print(f"⚠️  Full generation failed (this is expected without proper API setup): {e}")
        else:
            print("⚠️  No OpenAI API key found, skipping full generation test")
        
        print("\n🎉 LangGraph integration test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_langgraph_generator())
    sys.exit(0 if success else 1)
