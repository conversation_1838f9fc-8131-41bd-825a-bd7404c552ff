import axios from "axios";

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || "http://localhost:8000",
  timeout: 30000,
  headers: {
    "Content-Type": "application/json",
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add any auth headers here if needed
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // Handle common errors
    if (error.response) {
      // Server responded with error status
      const message =
        error.response.data?.detail ||
        error.response.data?.message ||
        "An error occurred";
      throw new Error(message);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error("No response from server. Please check your connection.");
    } else {
      // Something else happened
      throw new Error(error.message || "An unexpected error occurred");
    }
  }
);

// API functions
export const generateProject = async (projectData) => {
  try {
    const response = await api.post("/api/generate", projectData);
    return response;
  } catch (error) {
    throw error;
  }
};

export const generateIntelligentProject = async (projectData) => {
  try {
    const response = await api.post("/api/generate/intelligent", projectData);
    return response;
  } catch (error) {
    throw error;
  }
};

export const getProjectStatus = async (projectId) => {
  try {
    const response = await api.get(`/api/status/${projectId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

export const getEnhancedProjectStatus = async (projectId) => {
  try {
    const response = await api.get(`/api/status/enhanced/${projectId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

export const getProjectAnalysis = async (projectId) => {
  try {
    const response = await api.get(`/api/analysis/${projectId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

export const getModificationSuggestions = async (projectId) => {
  try {
    const response = await api.get(`/api/suggestions/${projectId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

export const requestModifications = async (projectId, modificationData) => {
  try {
    const response = await api.post(
      `/api/modify/${projectId}`,
      modificationData
    );
    return response;
  } catch (error) {
    throw error;
  }
};

export const applyModifications = async (projectId, applyData) => {
  try {
    const response = await api.post(
      `/api/apply-modifications/${projectId}`,
      applyData
    );
    return response;
  } catch (error) {
    throw error;
  }
};

export const downloadProject = async (projectId) => {
  try {
    const response = await axios({
      method: "GET",
      url: `${api.defaults.baseURL}/api/download/${projectId}`,
      responseType: "blob",
      timeout: 60000, // 1 minute timeout for downloads
    });

    // Create blob link to download
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement("a");
    link.href = url;

    // Try to get filename from response headers
    const contentDisposition = response.headers["content-disposition"];
    let filename = `project_${projectId}.zip`;

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
      }
    }

    link.setAttribute("download", filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    return { success: true, filename };
  } catch (error) {
    if (error.response) {
      const message = error.response.data?.detail || "Download failed";
      throw new Error(message);
    }
    throw new Error("Download failed. Please try again.");
  }
};

export const getProjects = async () => {
  try {
    const response = await api.get("/api/projects");
    return response;
  } catch (error) {
    throw error;
  }
};

export const deleteProject = async (projectId) => {
  try {
    const response = await api.delete(`/api/projects/${projectId}`);
    return response;
  } catch (error) {
    throw error;
  }
};

// Health check
export const healthCheck = async () => {
  try {
    const response = await api.get("/health");
    return response;
  } catch (error) {
    throw error;
  }
};

// Test connection
export const testConnection = async () => {
  try {
    const response = await api.get("/");
    return response;
  } catch (error) {
    throw error;
  }
};

export default api;
