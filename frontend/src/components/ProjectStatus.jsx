import React, { useState, useEffect } from 'react'
import { CheckCircle, Clock, Download, AlertCircle, Loader2, RefreshCw } from 'lucide-react'
import { getProjectStatus, downloadProject } from '../services/api'

const ProjectStatus = ({ projectId, onCompleted }) => {
  const [status, setStatus] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [isDownloading, setIsDownloading] = useState(false)

  useEffect(() => {
    if (!projectId) return

    const fetchStatus = async () => {
      try {
        const statusData = await getProjectStatus(projectId)
        setStatus(statusData)
        setError('')

        // If completed, notify parent
        if (statusData.status === 'completed' && onCompleted) {
          setTimeout(() => onCompleted(), 2000)
        }
      } catch (err) {
        setError(err.message || 'Failed to fetch status')
      } finally {
        setIsLoading(false)
      }
    }

    // Initial fetch
    fetchStatus()

    // Poll for updates if not completed
    const interval = setInterval(() => {
      if (status?.status !== 'completed' && status?.status !== 'failed') {
        fetchStatus()
      }
    }, 2000)

    return () => clearInterval(interval)
  }, [projectId, status?.status, onCompleted])

  const handleDownload = async () => {
    if (!projectId) return

    setIsDownloading(true)
    try {
      await downloadProject(projectId)
    } catch (err) {
      setError(err.message || 'Failed to download project')
    } finally {
      setIsDownloading(false)
    }
  }

  const getStatusIcon = () => {
    if (!status) return <Loader2 className="w-6 h-6 animate-spin text-blue-500" />

    switch (status.status) {
      case 'pending':
        return <Clock className="w-6 h-6 text-yellow-500" />
      case 'generating':
        return <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
      case 'completed':
        return <CheckCircle className="w-6 h-6 text-green-500" />
      case 'failed':
        return <AlertCircle className="w-6 h-6 text-red-500" />
      default:
        return <Clock className="w-6 h-6 text-gray-500" />
    }
  }

  const getStatusColor = () => {
    if (!status) return 'bg-gray-200'

    switch (status.status) {
      case 'pending':
        return 'bg-yellow-200'
      case 'generating':
        return 'bg-blue-200'
      case 'completed':
        return 'bg-green-200'
      case 'failed':
        return 'bg-red-200'
      default:
        return 'bg-gray-200'
    }
  }

  const getProgressColor = () => {
    if (!status) return 'bg-gray-500'

    switch (status.status) {
      case 'pending':
        return 'bg-yellow-500'
      case 'generating':
        return 'bg-blue-500'
      case 'completed':
        return 'bg-green-500'
      case 'failed':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  if (isLoading) {
    return (
      <div className="max-w-2xl mx-auto">
        <div className="bg-white shadow-lg rounded-lg p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500 mr-3" />
            <span className="text-lg text-gray-600">Loading project status...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className={`px-6 py-4 ${getStatusColor()}`}>
          <div className="flex items-center">
            {getStatusIcon()}
            <div className="ml-3">
              <h2 className="text-xl font-bold text-gray-900">
                Project Generation Status
              </h2>
              <p className="text-gray-700">Project ID: {projectId}</p>
            </div>
          </div>
        </div>

        <div className="p-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div className="flex">
                <AlertCircle className="w-5 h-5 text-red-400 mr-2" />
                <p className="text-red-800">{error}</p>
              </div>
            </div>
          )}

          {status && (
            <div className="space-y-6">
              {/* Progress Bar */}
              <div>
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Progress</span>
                  <span>{status.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className={`h-2 rounded-full transition-all duration-300 ${getProgressColor()}`}
                    style={{ width: `${status.progress}%` }}
                  />
                </div>
              </div>

              {/* Current Step */}
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-2">Current Step</h3>
                <p className="text-gray-900">{status.current_step}</p>
              </div>

              {/* Status Details */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-700">Status</h4>
                  <p className="text-lg font-semibold capitalize text-gray-900">
                    {status.status}
                  </p>
                </div>
                {status.estimated_time_remaining && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-700">Est. Time Remaining</h4>
                    <p className="text-lg font-semibold text-gray-900">
                      {Math.ceil(status.estimated_time_remaining / 60)} min
                    </p>
                  </div>
                )}
              </div>

              {/* Error Message */}
              {status.error_message && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <h4 className="text-sm font-medium text-red-800 mb-2">Error Details</h4>
                  <p className="text-red-700">{status.error_message}</p>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-4 pt-4">
                {status.status === 'completed' && (
                  <button
                    onClick={handleDownload}
                    disabled={isDownloading}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    {isDownloading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Downloading...
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4 mr-2" />
                        Download Project
                      </>
                    )}
                  </button>
                )}

                {(status.status === 'generating' || status.status === 'pending') && (
                  <button
                    onClick={() => window.location.reload()}
                    className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Refresh Status
                  </button>
                )}
              </div>

              {/* Success Message */}
              {status.status === 'completed' && (
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <div className="flex">
                    <CheckCircle className="w-5 h-5 text-green-400 mr-2" />
                    <div>
                      <h4 className="text-sm font-medium text-green-800">
                        Project Generated Successfully!
                      </h4>
                      <p className="text-green-700 mt-1">
                        Your project has been generated and is ready for download.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ProjectStatus
