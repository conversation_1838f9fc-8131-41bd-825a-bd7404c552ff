import React, { useState } from "react";
import {
  <PERSON><PERSON>2,
  <PERSON><PERSON><PERSON>,
  Brain,
  Lightbulb,
  <PERSON><PERSON>s,
  Zap,
} from "lucide-react";
import { generateIntelligentProject } from "../services/api";

const IntelligentProjectForm = ({ onProjectGenerated }) => {
  const [formData, setFormData] = useState({
    prompt: "",
    ai_provider: "openai",
    project_name: "",
    preferred_technologies: [],
    constraints: [],
    target_platform: [],
    complexity_level: "medium",
    include_tests: true,
    include_documentation: true,
    include_deployment: false,
    include_ci_cd: false,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [showAdvanced, setShowAdvanced] = useState(false);

  const aiProviders = [
    {
      value: "openai",
      label: "OpenAI GPT-4",
      icon: Sparkles,
      description: "Most advanced reasoning and code generation",
    },
    {
      value: "huggingface",
      label: "Hugging Face",
      icon: Lightbulb,
      description: "Open source models and community-driven",
    },
    {
      value: "grok",
      label: "Grok",
      icon: Zap,
      description: "X.AI's fast and efficient model",
    },
  ];

  const complexityLevels = [
    {
      value: "simple",
      label: "Simple",
      description: "Basic functionality, minimal setup",
    },
    {
      value: "medium",
      label: "Medium",
      description: "Standard features, good practices",
    },
    {
      value: "complex",
      label: "Complex",
      description: "Advanced features, scalable architecture",
    },
    {
      value: "enterprise",
      label: "Enterprise",
      description: "Production-ready, full-featured",
    },
  ];

  const commonTechnologies = [
    "React",
    "Vue",
    "Angular",
    "Next.js",
    "TypeScript",
    "JavaScript",
    "Python",
    "FastAPI",
    "Django",
    "Flask",
    "Node.js",
    "Express",
    "PostgreSQL",
    "MongoDB",
    "Redis",
    "Docker",
    "Kubernetes",
    "AWS",
    "Vercel",
    "Tailwind CSS",
    "Material-UI",
    "Bootstrap",
  ];

  const platforms = [
    { value: "web", label: "Web Application" },
    { value: "mobile", label: "Mobile App" },
    { value: "desktop", label: "Desktop Application" },
    { value: "api", label: "API Service" },
    { value: "cli", label: "Command Line Tool" },
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleArrayToggle = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: prev[field].includes(value)
        ? prev[field].filter((item) => item !== value)
        : [...prev[field], value],
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      // Clean up form data
      const requestData = {
        ...formData,
        project_name: formData.project_name || null,
        preferred_technologies:
          formData.preferred_technologies.length > 0
            ? formData.preferred_technologies
            : null,
        constraints:
          formData.constraints.length > 0 ? formData.constraints : null,
        target_platform:
          formData.target_platform.length > 0 ? formData.target_platform : null,
      };

      const response = await generateIntelligentProject(requestData);
      onProjectGenerated(response);
    } catch (err) {
      setError(err.message || "Failed to generate project");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 bg-gradient-to-r from-purple-600 to-blue-600">
          <div className="flex items-center">
            <Brain className="w-8 h-8 text-white mr-3" />
            <div>
              <h2 className="text-2xl font-bold text-white">
                Intelligent Project Generator
              </h2>
              <p className="text-purple-100 mt-1">
                Describe your project idea and let AI create the perfect
                solution
              </p>
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {/* Project Prompt */}
          <div>
            <label
              htmlFor="prompt"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Describe Your Project Idea *
            </label>
            <textarea
              id="prompt"
              name="prompt"
              value={formData.prompt}
              onChange={handleInputChange}
              rows={6}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              placeholder="Example: I want to build a task management app for teams with real-time collaboration, file sharing, and project analytics. It should have a modern UI, user authentication, and be scalable for enterprise use..."
              required
            />
            <p className="text-sm text-gray-500 mt-1">
              Be as detailed as possible. Include features, target users,
              technical preferences, and any specific requirements.
            </p>
          </div>

          {/* AI Provider */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              AI Provider
            </label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {aiProviders.map((provider) => {
                const Icon = provider.icon;
                return (
                  <label
                    key={provider.value}
                    className={`relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                      formData.ai_provider === provider.value
                        ? "border-purple-500 bg-purple-50"
                        : "border-gray-300"
                    }`}
                  >
                    <input
                      type="radio"
                      name="ai_provider"
                      value={provider.value}
                      checked={formData.ai_provider === provider.value}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <Icon className="w-5 h-5 text-gray-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">
                        {provider.label}
                      </div>
                      <div className="text-sm text-gray-500">
                        {provider.description}
                      </div>
                    </div>
                  </label>
                );
              })}
            </div>
          </div>

          {/* Advanced Options Toggle */}
          <div className="border-t pt-4">
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center text-purple-600 hover:text-purple-700 font-medium"
            >
              <Settings className="w-4 h-4 mr-2" />
              {showAdvanced ? "Hide" : "Show"} Advanced Options
            </button>
          </div>

          {showAdvanced && (
            <div className="space-y-6 border-t pt-6">
              {/* Project Name */}
              <div>
                <label
                  htmlFor="project_name"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Project Name (Optional)
                </label>
                <input
                  type="text"
                  id="project_name"
                  name="project_name"
                  value={formData.project_name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Leave empty for AI to suggest"
                />
              </div>

              {/* Complexity Level */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Complexity Level
                </label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  {complexityLevels.map((level) => (
                    <label
                      key={level.value}
                      className={`relative flex flex-col p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                        formData.complexity_level === level.value
                          ? "border-purple-500 bg-purple-50"
                          : "border-gray-300"
                      }`}
                    >
                      <input
                        type="radio"
                        name="complexity_level"
                        value={level.value}
                        checked={formData.complexity_level === level.value}
                        onChange={handleInputChange}
                        className="sr-only"
                      />
                      <span className="font-medium text-gray-900">
                        {level.label}
                      </span>
                      <span className="text-xs text-gray-500 mt-1">
                        {level.description}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Target Platforms */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Target Platforms
                </label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                  {platforms.map((platform) => (
                    <label
                      key={platform.value}
                      className={`flex items-center p-2 border rounded cursor-pointer hover:bg-gray-50 ${
                        formData.target_platform.includes(platform.value)
                          ? "border-purple-500 bg-purple-50"
                          : "border-gray-300"
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={formData.target_platform.includes(
                          platform.value
                        )}
                        onChange={() =>
                          handleArrayToggle("target_platform", platform.value)
                        }
                        className="sr-only"
                      />
                      <span className="text-sm text-gray-900">
                        {platform.label}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Preferred Technologies */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Preferred Technologies (Optional)
                </label>
                <div className="grid grid-cols-3 md:grid-cols-6 gap-2">
                  {commonTechnologies.map((tech) => (
                    <label
                      key={tech}
                      className={`flex items-center p-2 border rounded cursor-pointer hover:bg-gray-50 text-center ${
                        formData.preferred_technologies.includes(tech)
                          ? "border-purple-500 bg-purple-50"
                          : "border-gray-300"
                      }`}
                    >
                      <input
                        type="checkbox"
                        checked={formData.preferred_technologies.includes(tech)}
                        onChange={() =>
                          handleArrayToggle("preferred_technologies", tech)
                        }
                        className="sr-only"
                      />
                      <span className="text-xs text-gray-900 w-full">
                        {tech}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Options */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="include_tests"
                    checked={formData.include_tests}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="ml-2 text-sm text-gray-900">
                    Include Tests
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="include_documentation"
                    checked={formData.include_documentation}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="ml-2 text-sm text-gray-900">
                    Documentation
                  </span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="include_deployment"
                    checked={formData.include_deployment}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="ml-2 text-sm text-gray-900">Deployment</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="include_ci_cd"
                    checked={formData.include_ci_cd}
                    onChange={handleInputChange}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="ml-2 text-sm text-gray-900">CI/CD</span>
                </label>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <div className="pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md text-white bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  AI is analyzing and generating...
                </>
              ) : (
                <>
                  <Brain className="w-5 h-5 mr-2" />
                  Generate Intelligent Project
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default IntelligentProjectForm;
