import React, { useEffect, useRef } from "react";
import { createTwoFilesPatch } from "diff";
import { html } from "diff2html";
import "diff2html/bundles/css/diff2html.min.css";

const DiffViewer = ({ originalContent, modifiedContent, fileName }) => {
  const containerRef = useRef(null);

  useEffect(() => {
    if (containerRef.current && originalContent !== undefined && modifiedContent !== undefined) {
      // Create unified diff
      const diff = createTwoFilesPatch(
        fileName || "original",
        fileName || "modified",
        originalContent,
        modifiedContent,
        "Original",
        "Modified"
      );

      // Generate HTML from diff
      const diffHtml = html(diff, {
        drawFileList: false,
        matching: "lines",
        outputFormat: "side-by-side",
        synchronisedScroll: true,
        highlight: true,
      });

      // Set the HTML content
      containerRef.current.innerHTML = diffHtml;
    }
  }, [originalContent, modifiedContent, fileName]);

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between px-4 py-2 bg-gray-100 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">Diff View: {fileName}</span>
        </div>
        <div className="flex items-center space-x-4 text-xs text-gray-500">
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-red-200 rounded"></div>
            <span>Removed</span>
          </div>
          <div className="flex items-center space-x-1">
            <div className="w-3 h-3 bg-green-200 rounded"></div>
            <span>Added</span>
          </div>
        </div>
      </div>

      {/* Diff Content */}
      <div className="flex-1 overflow-auto">
        <div
          ref={containerRef}
          className="diff-viewer"
          style={{
            fontSize: "14px",
            lineHeight: "1.4",
          }}
        />
      </div>
    </div>
  );
};

export default DiffViewer;
