import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> } from "lucide-react";

const Header = () => {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-6">
          <div className="flex items-center space-x-3">
            <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
              <Brain className="w-7 h-7 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Intelligent AI Project Generator
              </h1>
              <p className="text-sm text-gray-500">
                Advanced AI-powered project creation with modification
                capabilities
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Sparkles className="w-4 h-4 text-purple-500" />
              <span>Cursor AI-like Intelligence</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Zap className="w-4 h-4 text-blue-500" />
              <span>Real-time Modifications</span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
