import React, { useState, useEffect } from "react";
import {
  Download,
  Trash2,
  Refresh<PERSON><PERSON>,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
} from "lucide-react";
import { getProjects, downloadProject, deleteProject } from "../services/api";

const ProjectList = () => {
  const [projects, setProjects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [downloadingProjects, setDownloadingProjects] = useState(new Set());
  const [deletingProjects, setDeletingProjects] = useState(new Set());

  useEffect(() => {
    fetchProjects();
  }, []);

  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      const data = await getProjects();
      setProjects(data.projects || []);
      setError("");
    } catch (err) {
      setError(err.message || "Failed to fetch projects");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (projectId) => {
    setDownloadingProjects((prev) => new Set([...prev, projectId]));
    try {
      await downloadProject(projectId);
    } catch (err) {
      setError(err.message || "Failed to download project");
    } finally {
      setDownloadingProjects((prev) => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    }
  };

  const handleDelete = async (projectId) => {
    if (!confirm("Are you sure you want to delete this project?")) return;

    setDeletingProjects((prev) => new Set([...prev, projectId]));
    try {
      await deleteProject(projectId);
      setProjects((prev) => prev.filter((p) => p.project_id !== projectId));
    } catch (err) {
      setError(err.message || "Failed to delete project");
    } finally {
      setDeletingProjects((prev) => {
        const newSet = new Set(prev);
        newSet.delete(projectId);
        return newSet;
      });
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case "pending":
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case "generating":
        return <Loader2 className="w-5 h-5 animate-spin text-blue-500" />;
      case "completed":
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case "failed":
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusBadge = (status) => {
    const baseClasses =
      "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";

    switch (status) {
      case "pending":
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case "generating":
        return `${baseClasses} bg-blue-100 text-blue-800`;
      case "completed":
        return `${baseClasses} bg-green-100 text-green-800`;
      case "failed":
        return `${baseClasses} bg-red-100 text-red-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="max-w-6xl mx-auto">
        <div className="bg-white shadow-lg rounded-lg p-6">
          <div className="flex items-center justify-center">
            <Loader2 className="w-8 h-8 animate-spin text-blue-500 mr-3" />
            <span className="text-lg text-gray-600">Loading projects...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-white">My Projects</h2>
              <p className="text-blue-100 mt-1">
                Manage your generated projects
              </p>
            </div>
            <button
              onClick={fetchProjects}
              className="flex items-center px-4 py-2 bg-white bg-opacity-20 text-white rounded-md hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        <div className="p-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div className="flex">
                <AlertCircle className="w-5 h-5 text-red-400 mr-2" />
                <p className="text-red-800">{error}</p>
              </div>
            </div>
          )}

          {projects.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg
                  className="w-16 h-16 mx-auto"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No projects yet
              </h3>
              <p className="text-gray-500">
                Create your first project to get started!
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {projects.map((project) => (
                <div
                  key={project.project_id}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {getStatusIcon(project.status)}
                      <div>
                        <h3 className="text-lg font-medium text-gray-900">
                          {project.project_name ||
                            project.suggested_name ||
                            `Project ${project.project_id.slice(0, 8)}...`}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {project.project_description ||
                            "AI-generated project"}
                        </p>
                        <div className="flex items-center space-x-2 mt-2">
                          <span className={getStatusBadge(project.status)}>
                            {project.status}
                          </span>
                          {project.progress !== undefined && (
                            <span className="text-sm text-gray-500">
                              {project.progress}% complete
                            </span>
                          )}
                          {project.project_type && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                              {project.project_type}
                            </span>
                          )}
                        </div>

                        {/* Technology Stack Display */}
                        {project.analysis?.technology_stack && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            {project.analysis.technology_stack.frontend?.map(
                              (tech, index) => (
                                <span
                                  key={`frontend-${index}`}
                                  className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded"
                                >
                                  {tech}
                                </span>
                              )
                            )}
                            {project.analysis.technology_stack.backend?.map(
                              (tech, index) => (
                                <span
                                  key={`backend-${index}`}
                                  className="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded"
                                >
                                  {tech}
                                </span>
                              )
                            )}
                            {project.analysis.technology_stack.database?.map(
                              (tech, index) => (
                                <span
                                  key={`database-${index}`}
                                  className="text-xs bg-orange-100 text-orange-800 px-2 py-1 rounded"
                                >
                                  {tech}
                                </span>
                              )
                            )}
                          </div>
                        )}

                        {/* Key Features */}
                        {project.analysis?.key_features &&
                          project.analysis.key_features.length > 0 && (
                            <div className="mt-2">
                              <p className="text-xs text-gray-500">
                                Features:{" "}
                                {project.analysis.key_features.join(", ")}
                              </p>
                            </div>
                          )}
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {project.file_size && (
                        <span className="text-sm text-gray-500">
                          {formatFileSize(project.file_size)}
                        </span>
                      )}

                      {project.status === "completed" &&
                        project.download_available && (
                          <button
                            onClick={() => handleDownload(project.project_id)}
                            disabled={downloadingProjects.has(
                              project.project_id
                            )}
                            className="flex items-center px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50"
                          >
                            {downloadingProjects.has(project.project_id) ? (
                              <>
                                <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                                Downloading...
                              </>
                            ) : (
                              <>
                                <Download className="w-4 h-4 mr-1" />
                                Download
                              </>
                            )}
                          </button>
                        )}

                      <button
                        onClick={() => handleDelete(project.project_id)}
                        disabled={deletingProjects.has(project.project_id)}
                        className="flex items-center px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                      >
                        {deletingProjects.has(project.project_id) ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                            Deleting...
                          </>
                        ) : (
                          <>
                            <Trash2 className="w-4 h-4 mr-1" />
                            Delete
                          </>
                        )}
                      </button>
                    </div>
                  </div>

                  {project.current_step && (
                    <div className="mt-3 text-sm text-gray-600">
                      <strong>Current step:</strong> {project.current_step}
                    </div>
                  )}

                  {project.progress !== undefined &&
                    project.status === "generating" && (
                      <div className="mt-3">
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${project.progress}%` }}
                          />
                        </div>
                      </div>
                    )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectList;
