import React, { useState } from "react";
import {
  Loader2,
  Sparkles,
  Code,
  Smartphone,
  Globe,
  Server,
  Database,
  Layers,
  Lightbulb,
  Zap,
} from "lucide-react";
import { generateProject } from "../services/api";

const ProjectForm = ({ onProjectGenerated }) => {
  const [formData, setFormData] = useState({
    project_name: "",
    project_type: "react",
    ai_provider: "openai",
    description: "",
    features: [],
    additional_requirements: "",
    include_tests: true,
    include_documentation: true,
    styling_framework: "",
    database_type: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const projectTypes = [
    {
      value: "react",
      label: "React App",
      icon: Globe,
      description: "Modern React application with Vite",
    },
    {
      value: "nextjs",
      label: "Next.js App",
      icon: Globe,
      description: "Full-stack React framework",
    },
    {
      value: "react_native",
      label: "React Native",
      icon: Smartphone,
      description: "Cross-platform mobile app",
    },
    {
      value: "python",
      label: "Python Project",
      icon: Code,
      description: "Python application with best practices",
    },
    {
      value: "fastapi",
      label: "FastAPI",
      icon: Server,
      description: "Modern Python web API",
    },
    {
      value: "django",
      label: "Django",
      icon: Server,
      description: "Full-featured Python web framework",
    },
    {
      value: "flask",
      label: "Flask",
      icon: Server,
      description: "Lightweight Python web framework",
    },
  ];

  const aiProviders = [
    { value: "openai", label: "OpenAI GPT-4", icon: Sparkles },
    { value: "huggingface", label: "Hugging Face", icon: Lightbulb },
    { value: "grok", label: "Grok", icon: Zap },
  ];

  const commonFeatures = [
    "Authentication",
    "Database Integration",
    "API Integration",
    "Testing Setup",
    "CI/CD Pipeline",
    "Docker Support",
    "Responsive Design",
    "State Management",
    "Routing",
    "Form Validation",
  ];

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleFeatureToggle = (feature) => {
    setFormData((prev) => ({
      ...prev,
      features: prev.features.includes(feature)
        ? prev.features.filter((f) => f !== feature)
        : [...prev.features, feature],
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      const response = await generateProject(formData);
      onProjectGenerated(response);
    } catch (err) {
      setError(err.message || "Failed to generate project");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white shadow-lg rounded-lg overflow-hidden">
        <div className="px-6 py-4 bg-gradient-to-r from-blue-600 to-purple-600">
          <h2 className="text-2xl font-bold text-white">Create New Project</h2>
          <p className="text-blue-100 mt-1">
            Generate a project skeleton using AI
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <p className="text-red-800">{error}</p>
            </div>
          )}

          {/* Project Name */}
          <div>
            <label
              htmlFor="project_name"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Project Name
            </label>
            <input
              type="text"
              id="project_name"
              name="project_name"
              value={formData.project_name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="my-awesome-project"
              required
            />
          </div>

          {/* Project Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Project Type
            </label>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {projectTypes.map((type) => {
                const Icon = type.icon;
                return (
                  <label
                    key={type.value}
                    className={`relative flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                      formData.project_type === type.value
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-300"
                    }`}
                  >
                    <input
                      type="radio"
                      name="project_type"
                      value={type.value}
                      checked={formData.project_type === type.value}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <Icon className="w-5 h-5 text-gray-600 mr-3" />
                    <div>
                      <div className="font-medium text-gray-900">
                        {type.label}
                      </div>
                      <div className="text-sm text-gray-500">
                        {type.description}
                      </div>
                    </div>
                  </label>
                );
              })}
            </div>
          </div>

          {/* AI Provider */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              AI Provider
            </label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {aiProviders.map((provider) => {
                const Icon = provider.icon;
                return (
                  <label
                    key={provider.value}
                    className={`relative flex items-center justify-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                      formData.ai_provider === provider.value
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-300"
                    }`}
                  >
                    <input
                      type="radio"
                      name="ai_provider"
                      value={provider.value}
                      checked={formData.ai_provider === provider.value}
                      onChange={handleInputChange}
                      className="sr-only"
                    />
                    <Icon className="w-4 h-4 text-gray-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">
                      {provider.label}
                    </span>
                  </label>
                );
              })}
            </div>
          </div>

          {/* Description */}
          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Project Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Describe what your project should do, its main features, and any specific requirements..."
              required
            />
          </div>

          {/* Features */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Features to Include
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-2">
              {commonFeatures.map((feature) => (
                <label
                  key={feature}
                  className={`flex items-center p-2 border rounded cursor-pointer hover:bg-gray-50 ${
                    formData.features.includes(feature)
                      ? "border-blue-500 bg-blue-50"
                      : "border-gray-300"
                  }`}
                >
                  <input
                    type="checkbox"
                    checked={formData.features.includes(feature)}
                    onChange={() => handleFeatureToggle(feature)}
                    className="sr-only"
                  />
                  <span className="text-sm text-gray-900">{feature}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Additional Requirements */}
          <div>
            <label
              htmlFor="additional_requirements"
              className="block text-sm font-medium text-gray-700 mb-2"
            >
              Additional Requirements (Optional)
            </label>
            <textarea
              id="additional_requirements"
              name="additional_requirements"
              value={formData.additional_requirements}
              onChange={handleInputChange}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Any specific libraries, frameworks, or configurations you want included..."
            />
          </div>

          {/* Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="include_tests"
                checked={formData.include_tests}
                onChange={handleInputChange}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-900">
                Include test setup
              </span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                name="include_documentation"
                checked={formData.include_documentation}
                onChange={handleInputChange}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-900">
                Include documentation
              </span>
            </label>
          </div>

          {/* Submit Button */}
          <div className="pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="w-full flex items-center justify-center px-4 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Generating Project...
                </>
              ) : (
                <>
                  <Sparkles className="w-5 h-5 mr-2" />
                  Generate Project
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProjectForm;
