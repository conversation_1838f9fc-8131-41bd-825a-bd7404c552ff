import React, { useState } from "react";
import ProjectForm from "./components/ProjectForm";
import IntelligentProjectForm from "./components/IntelligentProjectForm";
import ProjectStatus from "./components/ProjectStatus";
import IntelligentProjectStatus from "./components/IntelligentProjectStatus";
import ProjectList from "./components/ProjectList";
import Header from "./components/Header";
import "./App.css";

function App() {
  const [currentProject, setCurrentProject] = useState(null);
  const [activeTab, setActiveTab] = useState("intelligent");
  const [isIntelligentMode, setIsIntelligentMode] = useState(true);

  const handleProjectGenerated = (projectData) => {
    setCurrentProject(projectData);
    setActiveTab("status");
  };

  const handleProjectCompleted = () => {
    setCurrentProject(null);
    setActiveTab("projects");
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Mode Toggle */}
        <div className="mb-6">
          <div className="flex items-center justify-center">
            <div className="bg-white rounded-lg p-1 shadow-sm border">
              <button
                onClick={() => {
                  setIsIntelligentMode(true);
                  setActiveTab("intelligent");
                }}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  isIntelligentMode
                    ? "bg-purple-600 text-white"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                🧠 Intelligent Mode
              </button>
              <button
                onClick={() => {
                  setIsIntelligentMode(false);
                  setActiveTab("create");
                }}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  !isIntelligentMode
                    ? "bg-blue-600 text-white"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                ⚙️ Classic Mode
              </button>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {isIntelligentMode ? (
              <>
                <button
                  onClick={() => setActiveTab("intelligent")}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === "intelligent"
                      ? "border-purple-500 text-purple-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  🧠 AI Generator
                </button>
                <button
                  onClick={() => setActiveTab("status")}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === "status"
                      ? "border-purple-500 text-purple-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                  disabled={!currentProject}
                >
                  📊 Smart Status
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => setActiveTab("create")}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === "create"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  Create Project
                </button>
                <button
                  onClick={() => setActiveTab("status")}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === "status"
                      ? "border-blue-500 text-blue-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                  disabled={!currentProject}
                >
                  Generation Status
                </button>
              </>
            )}
            <button
              onClick={() => setActiveTab("projects")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "projects"
                  ? isIntelligentMode
                    ? "border-purple-500 text-purple-600"
                    : "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              My Projects
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === "intelligent" && (
          <IntelligentProjectForm onProjectGenerated={handleProjectGenerated} />
        )}

        {activeTab === "create" && (
          <ProjectForm onProjectGenerated={handleProjectGenerated} />
        )}

        {activeTab === "status" &&
          currentProject &&
          (isIntelligentMode ? (
            <IntelligentProjectStatus
              projectId={currentProject.project_id}
              onCompleted={handleProjectCompleted}
            />
          ) : (
            <ProjectStatus
              projectId={currentProject.project_id}
              onCompleted={handleProjectCompleted}
            />
          ))}

        {activeTab === "projects" && <ProjectList />}
      </main>
    </div>
  );
}

export default App;
