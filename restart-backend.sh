#!/bin/bash

echo "🔄 Restarting FastAPI backend server..."

# Find and kill existing uvicorn processes
echo "🛑 Stopping existing backend server..."
pkill -f "uvicorn app.main:app" || echo "No existing server found"

# Wait a moment for processes to stop
sleep 2

# Start the backend server
echo "🚀 Starting backend server..."
cd backend
source .venv/bin/activate
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!
cd ..

echo "✅ Backend server restarted!"
echo "🔧 Backend API: http://localhost:8000"
echo "📖 API Docs: http://localhost:8000/docs"
echo "🆔 Process ID: $BACKEND_PID"
echo ""
echo "💡 Use this script to restart the backend after making code changes"
echo "💡 Frontend will continue running uninterrupted"
