#!/usr/bin/env python3
"""
Test script to verify project generation improvements
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from app.services.intelligent_project_generator import IntelligentProjectGenerator
from app.models.project_models import IntelligentProjectRequest, ProjectAnalysis, TechnologyStack

def test_enhanced_fallback():
    """Test the enhanced fallback project structure generation"""
    print("Testing enhanced fallback project generation...")
    
    generator = IntelligentProjectGenerator()
    
    # Create a mock analysis for fullstack project
    analysis = ProjectAnalysis(
        project_type="fullstack",
        suggested_name="test-fullstack-app",
        technology_stack=TechnologyStack(
            frontend=["React", "TypeScript", "Vite"],
            backend=["FastAPI", "Python"],
            database=["SQLite"],
            styling=["Tailwind CSS"]
        ),
        architecture_pattern="MVC",
        estimated_complexity="medium",
        key_features=["User Authentication", "Dashboard", "API Integration", "Responsive Design"],
        technical_requirements=["Modern web standards", "RESTful API"],
        suggested_folder_structure={},
        dependencies={
            "frontend": ["react", "typescript", "vite", "tailwindcss"],
            "backend": ["fastapi", "uvicorn", "sqlalchemy"]
        },
        reasoning="Test analysis for fullstack application"
    )
    
    # Test enhanced fallback generation
    result = generator._generate_enhanced_fallback_project_structure(analysis, "Test error")
    
    print(f"Generated {len(result['files'])} files:")
    for file_path in sorted(result['files'].keys()):
        print(f"  - {file_path}")
    
    # Check if both frontend and backend files are generated
    frontend_files = [f for f in result['files'].keys() if f.startswith('frontend/')]
    backend_files = [f for f in result['files'].keys() if f.startswith('backend/')]
    
    print(f"\nFrontend files: {len(frontend_files)}")
    print(f"Backend files: {len(backend_files)}")
    
    # Verify key files exist
    expected_files = [
        'README.md',
        'frontend/package.json',
        'frontend/src/App.tsx',
        'backend/requirements.txt',
        'backend/main.py'
    ]
    
    missing_files = [f for f in expected_files if f not in result['files']]
    if missing_files:
        print(f"❌ Missing expected files: {missing_files}")
        return False
    else:
        print("✅ All expected files generated successfully!")
        return True

def test_frontend_only():
    """Test frontend-only project generation"""
    print("\nTesting frontend-only project generation...")
    
    generator = IntelligentProjectGenerator()
    
    analysis = ProjectAnalysis(
        project_type="frontend",
        suggested_name="test-frontend-app",
        technology_stack=TechnologyStack(
            frontend=["React", "TypeScript"],
            backend=[],
            database=[],
            styling=["Tailwind CSS"]
        ),
        architecture_pattern="SPA",
        estimated_complexity="simple",
        key_features=["Responsive UI", "Component Library"],
        technical_requirements=["Modern web standards"],
        suggested_folder_structure={},
        dependencies={"frontend": ["react", "typescript"]},
        reasoning="Test analysis for frontend-only application"
    )
    
    result = generator._generate_enhanced_fallback_project_structure(analysis, "Test error")
    
    frontend_files = [f for f in result['files'].keys() if f.startswith('frontend/')]
    backend_files = [f for f in result['files'].keys() if f.startswith('backend/')]
    
    print(f"Frontend files: {len(frontend_files)}")
    print(f"Backend files: {len(backend_files)}")
    
    if len(frontend_files) > 0 and len(backend_files) == 0:
        print("✅ Frontend-only generation working correctly!")
        return True
    else:
        print("❌ Frontend-only generation failed!")
        return False

def test_backend_only():
    """Test backend-only project generation"""
    print("\nTesting backend-only project generation...")
    
    generator = IntelligentProjectGenerator()
    
    analysis = ProjectAnalysis(
        project_type="api",
        suggested_name="test-api",
        technology_stack=TechnologyStack(
            frontend=[],
            backend=["FastAPI", "Python"],
            database=["PostgreSQL"],
            styling=[]
        ),
        architecture_pattern="REST",
        estimated_complexity="medium",
        key_features=["RESTful API", "Database Integration", "Authentication"],
        technical_requirements=["API documentation", "Database migrations"],
        suggested_folder_structure={},
        dependencies={"backend": ["fastapi", "sqlalchemy", "postgresql"]},
        reasoning="Test analysis for API-only application"
    )
    
    result = generator._generate_enhanced_fallback_project_structure(analysis, "Test error")
    
    frontend_files = [f for f in result['files'].keys() if f.startswith('frontend/')]
    backend_files = [f for f in result['files'].keys() if f.startswith('backend/')]
    
    print(f"Frontend files: {len(frontend_files)}")
    print(f"Backend files: {len(backend_files)}")
    
    if len(backend_files) > 0 and len(frontend_files) == 0:
        print("✅ Backend-only generation working correctly!")
        return True
    else:
        print("❌ Backend-only generation failed!")
        return False

if __name__ == "__main__":
    print("🚀 Testing Project Generation Improvements\n")
    
    tests = [
        test_enhanced_fallback,
        test_frontend_only,
        test_backend_only
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Project generation improvements are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
