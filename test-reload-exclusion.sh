#!/bin/bash

echo "🧪 Testing FastAPI reload behavior (reload disabled)..."

echo ""
echo "📁 Test 1: Creating files in generated_projects (should NOT trigger reload)..."

# Create test directory if it doesn't exist
mkdir -p backend/generated_projects/test

# Create some test files
echo "Test file 1" > backend/generated_projects/test/test1.txt
echo "Test file 2" > backend/generated_projects/test/test2.py
echo "Test file 3" > backend/generated_projects/test/test3.json

echo "✅ Test files created in generated_projects:"
ls -la backend/generated_projects/test/

echo ""
echo "🔍 FastAPI should NOT have reloaded (reload is disabled)"
echo ""

sleep 2

echo "📁 Test 2: Creating a file in app directory (should NOT trigger reload)..."

# Create a test file in the app directory
echo "# Test file - safe to delete" > backend/app/test_reload_trigger.py

echo "✅ Test file created in app directory"
echo ""
echo "🔍 FastAPI should NOT have reloaded (reload is disabled)"
echo ""

sleep 2

echo "🧹 Cleaning up test files..."

# Clean up
rm -rf backend/generated_projects/test
rm -f backend/app/test_reload_trigger.py

echo "✅ Test completed!"
echo ""
echo "📊 Expected results with reload disabled:"
echo "  ❌ Files in generated_projects/ should NOT trigger reload"
echo "  ❌ Files in app/ should NOT trigger reload"
echo "  🔄 Manual restart required for any backend changes"
echo ""
echo "Check your FastAPI server logs to verify NO reloads occurred."
