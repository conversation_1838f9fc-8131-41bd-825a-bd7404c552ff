#!/bin/bash

echo "🚀 Starting AI Project Generator in development mode with precise reload control..."

# Function to kill background processes on exit
cleanup() {
    echo "🛑 Stopping servers..."
    kill $BACKEND_PID $FRONTEND_PID 2>/dev/null
    exit
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start backend with reload completely disabled
echo "🐍 Starting backend server (reload disabled)..."
cd backend
source .venv/bin/activate

# Disable reload completely to prevent any file changes from restarting server
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Start frontend
echo "⚛️  Starting frontend server..."
cd frontend
npm run dev &
FRONTEND_PID=$!
cd ..

echo "✅ Servers started with reload completely disabled!"
echo "🌐 Frontend: http://localhost:5173"
echo "🔧 Backend API: http://localhost:8000"
echo "📖 API Docs: http://localhost:8000/docs"
echo ""
echo "🚫 FastAPI reload is DISABLED - no file changes will restart the server"
echo "🔄 To apply backend changes, manually restart the server (Ctrl+C then restart)"
echo ""
echo "Press Ctrl+C to stop both servers"

# Wait for background processes
wait
